# Simple Makefile for a Go project

# Build the application
all: build test
templ-install:
	@if ! command -v templ > /dev/null; then \
		read -p "Go's 'templ' is not installed on your machine. Do you want to install it? [Y/n] " choice; \
		if [ "$$choice" != "n" ] && [ "$$choice" != "N" ]; then \
			go install github.com/a-h/templ/cmd/templ@latest; \
			if [ ! -x "$$(command -v templ)" ]; then \
				echo "templ installation failed. Exiting..."; \
				exit 1; \
			fi; \
		else \
			echo "You chose not to install templ. Exiting..."; \
			exit 1; \
		fi; \
	fi

build: templ-install
	TEMPL_EXPERIMENT=rawgo templ fmt ./pkg/templates
	TEMPL_EXPERIMENT=rawgo templ generate
	bun run build --base=/static
	go build -ldflags="-X 'main.Version=`git rev-parse --short HEAD`'" -o ./loms ./cmd/web

build-prod: templ-install
	TEMPL_EXPERIMENT=rawgo templ fmt ./pkg/templates
	TEMPL_EXPERIMENT=rawgo templ generate
	bun run build --base=/static
	env GOOS=linux GOARCH=amd64 go build -ldflags="-X 'main.Version=`git rev-parse --short HEAD`'" -o ./loms ./cmd/web


# Run the application
run:
	@go run cmd/web/main.go

# Test the application
test:
	@echo "Testing..."
	@go test ./... -v

# Clean the binary
clean:
	@echo "Cleaning..."
	@rm -f loms

# Live Reload
watch:
	@if command -v air > /dev/null; then \
            air; \
        else \
            read -p "Go's 'air' is not installed on your machine. Do you want to install it? [Y/n] " choice; \
            if [ "$$choice" != "n" ] && [ "$$choice" != "N" ]; then \
                go install github.com/air-verse/air@latest; \
                air; \
            else \
                echo "You chose not to install air. Exiting..."; \
                exit 1; \
            fi; \
        fi

.PHONY: all build run test clean watch templ-install
