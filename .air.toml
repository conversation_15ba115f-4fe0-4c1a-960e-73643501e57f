root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = []
bin = "./loms"
# cmd = " TEMPL_EXPERIMENT=rawgo templ fmt ./templates && templ generate && go build -o ./loms ./cmd/web"
  cmd = "make build"
  delay = 100
  exclude_dir = ["node_modules", "pkg/static", "tmp", "vendor", "testdata", "dbs"]
  exclude_file = []
exclude_regex = [".*_templ.go", "_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
include_ext = ["go", "tpl", "tmpl", "templ", "yaml", "html", "css", "js"]

  include_file = []
  kill_delay = "0s"
  log = "build-errors.log"
  poll = false
  poll_interval = 0
  post_cmd = []
  pre_cmd = []
  rerun = false
  rerun_delay = 500
  send_interrupt = false
  stop_on_error = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  main_only = false
  time = false

[misc]
  clean_on_exit = false

[screen]
  clear_on_rebuild = false
  keep_scroll = true
