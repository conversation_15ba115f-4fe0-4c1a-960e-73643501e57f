SELECT l.id         loms_loc_id,
	   p.id         loms_pickup_id,
	   CASE
		   WHEN o.pickup_id IS NOT NULL THEN '○'
		   ELSE '×'
		   END   AS 受注有無,
	   CASE
		   WHEN o2025.pickup_id IS NOT NULL THEN '○'
		   ELSE '×'
		   END   AS '202503以降受注有無',
	   CASE
		   WHEN l.lat_lng IS NOT NULL AND l.lat_lng <> ',' THEN '○'
		   ELSE '×'
		   END   AS 緯度経度有無,
	   p.roms_id AS pickup_cd,
	   p.nm,	   l.zip,	   l.pref_id,	   l.city_cd,	   l.add1,
	   l.add1_kana,	   l.add2,	   l.add2_kana,	   l.add3,	   l.add3_kana,
	   l.tel,	   l.fax,	   l.lat_lng
FROM m_pickups p
		 LEFT JOIN m_pickup_group pg ON p.id = pg.pickup_id
		 LEFT JOIN m_locations l ON l.id = p.location_id
		 LEFT JOIN (SELECT DISTINCT pickup_id
					FROM t_orders
					WHERE deleted_at IS NULL) o ON p.id = o.pickup_id

		 LEFT JOIN (SELECT DISTINCT pickup_id
					FROM t_orders
					WHERE deleted_at IS NULL and ship_date >= '2025-03-01') o2025 ON p.id = o2025.pickup_id
WHERE pg.group_id = 1
  AND p.deleted_at IS NULL
ORDER BY 受注有無 DESC, 緯度経度有無 DESC, '202503以降受注有無', pickup_cd;

select l.id       loms_loc_id,
	   a.id       loms_area_id,
	   (select CASE
				   WHEN o.dest_cd IS NOT NULL THEN '○'
				   ELSE '×'
				   END AS 受注有無
		from m_dest_spot s
				 LEFT JOIN m_dest_group g ON g.dest_spot_id = s.id
				 LEFT JOIN (SELECT distinct dest_cd
							FROM t_orders
							WHERE deleted_at IS NULL) o ON g.dest_cd = o.dest_cd
		where s.dest_area_id = a.id
		  and g.group_id = 1
		  and g.deleted_at is null
		order by 1 desc
		limit 1)  受注有無,
	   (select CASE
				   WHEN o.dest_cd IS NOT NULL THEN '○'
				   ELSE '×'
				   END AS 受注有無
		from m_dest_spot s
				 LEFT JOIN m_dest_group g ON g.dest_spot_id = s.id
				 LEFT JOIN (SELECT distinct dest_cd
							FROM t_orders
							WHERE deleted_at IS NULL and ship_date >= '2025-03-01') o ON g.dest_cd = o.dest_cd
		where s.dest_area_id = a.id
		  and g.group_id = 1
		  and g.deleted_at is null
		order by 1 desc
		limit 1)  '202503以降受注有無',

	   CASE
		   WHEN l.lat_lng IS NOT NULL AND l.lat_lng <> ',' THEN '○'
		   ELSE '×'
		   END AS 緯度経度有無,
	   dc.name 会社名,
	   a.roms_id  da_no,
	   a.nm    AS area_nm,
	   a.kana  AS area_kana,
	   l.zip,	   l.pref_id,	   l.city_cd,	   l.add1,	   l.add1_kana,
	   l.add2,	   l.add2_kana,	   l.add3,	   l.add3_kana,
	   l.tel,	   l.fax,	   l.lat_lng
from m_dest_area a
		 LEFT JOIN m_dest_company dc on dc.id = a.dest_company_id
		 LEFT JOIN m_locations l on a.location_id = l.id
WHERE a.id in (select a.id
			   from m_dest_area a
						LEFT JOIN m_dest_spot s ON s.dest_area_id = a.id
						LEFT JOIN m_dest_group g ON g.dest_spot_id = s.id
			   where g.group_id = 1
				 and g.deleted_at is null)
  and a.deleted_at is null

ORDER BY 受注有無 DESC, 緯度経度有無 DESC, '202503以降受注有無', a.id; -- 1821


select dg.dest_cd
from m_dest_group dg
		 LEFT JOIN m_dest_spot ds on ds.id = dg.dest_spot_id
where 2676 = ds.dest_area_id
  and dg.deleted_at is null
  and ds.deleted_at is null order by dest_cd;


select ds.nm, spot
from m_dest_spot ds
where 2676 = ds.dest_area_id

  and ds.deleted_at is null order by roms_id;


select group_concat(dest_cd, ',') as dest_codes_csv
from (
		 select distinct dg.dest_cd
		 from m_dest_group dg
				  left join m_dest_spot ds on ds.id = dg.dest_spot_id
		 where 2676 = ds.dest_area_id
		   and dg.deleted_at is null
		   and ds.deleted_at is null
		 order by dg.dest_cd
	 );
