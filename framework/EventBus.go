package framework

import "sync"

type EventBus struct {
	channels map[string]chan map[string]interface{}
	mu       sync.Mutex
}

func NewEventBus() *EventBus {
	return &EventBus{
		channels: make(map[string]chan map[string]interface{}),
	}
}

func (eb *EventBus) Publish(topic string, eventData map[string]interface{}) {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	if ch, exists := eb.channels[topic]; exists {
		ch <- eventData
	}
}

func (eb *EventBus) Subscribe(topic string) <-chan map[string]interface{} {
	eb.mu.Lock()
	defer eb.mu.Unlock()
	ch := make(chan map[string]interface{})
	eb.channels[topic] = ch
	return ch
}
