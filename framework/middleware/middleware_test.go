package middleware

import (
	"loms/framework/tests"
	"loms/pkg/db/sqlc"
	"os"
	"testing"

	"loms/config"
	"loms/pkg/services"
)

var (
	c   *services.Container
	usr *sqlc.User
)

func TestMain(m *testing.M) {
	// Set the environment to test
	config.SwitchEnvironment(config.EnvTest)

	// Create a new container
	c = services.NewContainer()

	// Create a user
	var err error
	if usr, err = tests.CreateUser(c.Db); err != nil {
		panic(err)
	}

	// Run tests
	exitVal := m.Run()

	// Shutdown the container
	if err = c.Shutdown(); err != nil {
		panic(err)
	}

	os.Exit(exitVal)
}
