package unpoly

import (
	"github.com/labstack/echo/v4"
)

// Request headers:
const (
	HeaderVersion     = "X-Up-Version"
	HeaderContext     = "X-Up-Context"
	HeaderFailContext = "X-Up-Fail-Context"
	HeaderMode        = "X-Up-Mode"
	HeaderFailMode    = "X-Up-Fail-Mode"
	HeaderTarget      = "X-Up-Target"
	HeaderFailTarget  = "X-Up-Fail-Target"
	HeaderValidate    = "X-Up-Validate"
)

// Response headers:
const (
	HeaderLocation     = "X-Up-Location"
	HeaderMethod       = "X-Up-Method"
	HeaderEvents       = "X-Up-Events"
	HeaderDismissLayer = "X-Up-Dismiss-Layer"
	HeaderTitle        = "X-Up-Title"
)

type (

	// Request contains data that Unpoly provides during requests
	Request struct {
		Enabled     bool
		Context     string
		FailContext string
		Mode        string
		FailMode    string
		Target      string
		FailTarget  string
		Validate    string
	}

	// Response contain data that the server can communicate back to Unpoly
	Response struct {
		Location string
		Method   string
		Target   string
		Events   string
		Title    string
	}
)

// GetRequest extracts Unpoly data from the request
func GetRequest(ctx echo.Context) Request {
	return Request{
		Enabled:     ctx.Request().Header.Get(HeaderVersion) != "",
		Context:     ctx.Request().Header.Get(HeaderContext),
		FailContext: ctx.Request().Header.Get(HeaderFailContext),
		Mode:        ctx.Request().Header.Get(HeaderMode),
		FailMode:    ctx.Request().Header.Get(HeaderFailMode),
		Target:      ctx.Request().Header.Get(HeaderTarget),
		FailTarget:  ctx.Request().Header.Get(HeaderFailTarget),
		Validate:    ctx.Request().Header.Get(HeaderValidate),
	}
}

// Apply applies data from a Response to a server response
func (r Response) Apply(ctx echo.Context) {
	if r.Location != "" {
		ctx.Response().Header().Set(HeaderLocation, r.Location)
		ctx.Response().Header().Set("Location", r.Location)
		ctx.Response().WriteHeader(302)
		if r.Method != "" {
			ctx.Response().Header().Set(HeaderMethod, r.Method)
		} else {
			ctx.Response().Header().Set(HeaderMethod, "GET")
		}
	}

	if r.Target != "" {
		ctx.Response().Header().Set(HeaderTarget, r.Target)
	}

	if r.Events != "" {
		ctx.Response().Header().Set(HeaderEvents, r.Events)
	}

	if r.Title != "" {
		ctx.Response().Header().Set(HeaderTitle, r.Title)
	}

}
