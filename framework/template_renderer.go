package framework

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"loms/config"
	"loms/framework/funcs"
	"loms/framework/page"
	"loms/framework/unpoly"
	"loms/pkg/templates/layouts"
	"loms/pkg/types"
	"strings"
)

// TemplateRenderer provides a flexible and easy to use method of rendering Templ templates while
// also providing caching and/or hot-reloading depending on your current environment
type TemplateRenderer struct {
	// For page caching
	*pageCache

	// config stores application configuration
	config *config.Config

	// funcs stores functions to be used in templates
	funcs *funcs.Funcs

	appVersion string
	appEnv     string
}

// NewTemplateRenderer creates a new TemplRenderer
func NewTemplateRenderer(cfg *config.Config, cache *CacheClient, web *echo.Echo, appVer string, env string) *TemplateRenderer {
	return &TemplateRenderer{
		pageCache: NewPageCache(cfg, cache),
		config:    cfg,
		funcs:     funcs.NewFuncs(web),

		appVersion: appVer,
		appEnv:     env,
	}
}

func (t *TemplateRenderer) RenderPageAuth(ctx echo.Context, page *page.Page) error {
	return t.renderPageInternal(ctx, page, true, false)
}

func (t *TemplateRenderer) RenderPage(ctx echo.Context, page *page.Page) error {
	return t.renderPageInternal(ctx, page, false, false)
}

func (t *TemplateRenderer) RenderPartial(ctx echo.Context, page *page.Page) error {
	return t.renderPageInternal(ctx, page, false, true)
}

// RenderPage renders a Page as an HTTP response
func (t *TemplateRenderer) renderPageInternal(ctx echo.Context, page *page.Page, isAuth bool, noLayout bool) error {
	var err error

	// Use the app name in configuration if a value was not set
	if page.AppName == "" {
		page.AppName = t.config.App.Name
	}
	page.AppVersion = t.appVersion
	page.AppEnv = t.appEnv

	if isAuth {
		page.TemplLayout = layouts.Auth
	} else {

		if page.TemplLayout == nil {
			page.TemplLayout = layouts.Main
		}

		if page.UP.Request.Enabled {
			if page.UP.Request.Mode == "modal" {
				page.TemplLayout = layouts.Model
			} else {
				setTitleHeader(page)
				page.TemplLayout = layouts.Plain
			}
		}
	}

	// Set the status code
	ctx.Response().Status = page.StatusCode

	// Set any headers
	for k, v := range page.Headers {
		ctx.Response().Header().Set(k, v)
	}

	// Apply the Unpoly response, if one
	if page.UP.Response != nil {
		page.UP.Response.Apply(ctx)
	}

	// Add Page and Funcs to templ context so they don't have to be passed around everywhere
	templCtx := context.WithValue(ctx.Request().Context(), types.TemplCtxKeyPage, page)
	templCtx = context.WithValue(templCtx, types.TemplCtxKeyFuncs, t.funcs)

	//Render template
	buf := &bytes.Buffer{}
	if noLayout {
		err = layouts.NoLayout(page.TemplComponent).Render(templCtx, buf)
	} else {
		err = page.TemplLayout(page.TemplComponent).Render(templCtx, buf)
	}
	if err != nil {
		return err
	}

	// Cache this page, if caching was enabled
	//t.cachePage(ctx, page, buf)

	return ctx.HTMLBlob(ctx.Response().Status, buf.Bytes())
}

func setTitleHeader(page *page.Page) {
	if page.UP.Response == nil {
		page.UP.Response = &unpoly.Response{}
	}
	titleStr := page.AppName
	if page.Title != "" {
		titleStr = fmt.Sprintf(`%s | %s`, page.AppName, page.Title)
	}
	title, _ := json.Marshal(titleStr)
	page.UP.Response.Title = escapeHighASCII(string(title))
}

func escapeHighASCII(s string) string {
	unicodeEscape := func(r rune) string {
		return fmt.Sprintf("\\u%04x", r)
	}
	var result strings.Builder
	for _, r := range s {
		if r > 127 {
			result.WriteString(unicodeEscape(r))
		} else {
			result.WriteRune(r)
		}
	}
	return result.String()
}
