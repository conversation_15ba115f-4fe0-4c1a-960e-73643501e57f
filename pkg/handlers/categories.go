package handlers

import (
	"loms/framework/log"
	views "loms/pkg/templates/pages/categories"
	"net/http"

	"loms/framework"
	"loms/framework/form"
	"loms/framework/middleware"
	"loms/framework/msg"
	"loms/framework/page"
	"loms/pkg/services"
	"loms/pkg/types"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)

type Categories struct {
	Service *services.MaterialCategoryCrudService
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(Categories))
}

func (c *Categories) Init(container *services.Container) error {
	c.TemplateRenderer = container.TemplateRenderer
	c.Service = container.MaterialCategoryCrudService
	return nil
}

func (c *Categories) Routes(g *echo.Group) {
	group := g.Group("/categories", middleware.RequireAuthentication())
	group.GET("", c.ListPage).Name = types.RouteCategories
	group.GET("/details/:id", c.DetailsPage).Name = types.RouteCategoriesDetails
	group.DELETE("/delete/:id", c.Delete).Name = types.RouteCategoriesDelete
	group.GET("/add", c.AddNewPage).Name = types.RouteCategoriesAddNew
	group.POST("/update", c.Save).Name = types.RouteCategoriesUpdate
}

func (c *Categories) ListPage(ctx echo.Context) error {
	input := new(types.PageRequest)
	if err := ctx.Bind(input); err != nil {
		log.Default().Error("Error binding input", "err", err)
		return ctx.String(http.StatusBadRequest, "Invalid request parameters")
	}

	entities, err := c.Service.FindAllPageable(ctx.Request().Context(), types.NewPageRequestOf(input))
	if err != nil {
		log.Default().Error("Error fetching categories", "err", err)
		return ctx.String(http.StatusInternalServerError, "Failed to fetch categories")
	}

	data := types.ToMaterialCategoryFormPage(entities)
	p := page.New(ctx).View(views.Index(data)).Titled("材料カテゴリ")
	return c.RenderPage(ctx, p)
}

func (c *Categories) Delete(ctx echo.Context) error {
	var id int64
	if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
		return c.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}
	_, err := c.Service.FindByID(ctx.Request().Context(), id)
	if err != nil {
		log.Default().Error("Error fetching category details", "err", err)
		return ctx.String(http.StatusInternalServerError, "Failed to fetch category details")
	}

	if err = c.Service.Delete(ctx.Request().Context(), id); err != nil {
		log.Default().Error("Error deleting category", "err", err)
		msg.Warning(ctx, "エラーが発生しました")
	} else {
		msg.Success(ctx, successMsg)
	}
	return c.HandleSaveResponse(true, types.RouteCategories, ctx)
}

func (c *Categories) DetailsPage(ctx echo.Context) error {
	formValue := form.Get[types.MaterialCategoryForm](ctx)
	var data *types.MaterialCategoryForm
	if formValue.IsSubmitted() {
		data = formValue
	} else {
		var id int64
		if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
			return c.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
		}
		entity, err := c.Service.FindByID(ctx.Request().Context(), id)
		if err != nil {
			log.Default().Error("Error fetching category details", "err", err)
			return ctx.String(http.StatusInternalServerError, "Failed to fetch category details")
		}
		data = types.FromDomainMaterialCategory(entity)
	}
	data.IsEdit = true
	p := page.New(ctx).View(views.CategoriesForm(data)).Titled("材料カテゴリ 詳細")
	return c.RenderPage(ctx, p)
}

func (c *Categories) AddNewPage(ctx echo.Context) error {
	data := form.Get[types.MaterialCategoryForm](ctx)
	p := page.New(ctx).View(views.CategoriesForm(data)).Titled("材料カテゴリ 新規登録")
	return c.RenderPage(ctx, p)
}

func (c *Categories) Save(ctx echo.Context) error {
	var formValue types.MaterialCategoryForm

	err := form.Submit(ctx, &formValue)
	if err != nil {
		return c.handleFormError(ctx, formValue.ID, err)
	}

	toBeSavedEntity, err := formValue.ToDomainMaterialCategory()
	if err != nil {
		log.Default().Error("Error converting form to domain entity", "err", err)
		return ctx.String(http.StatusInternalServerError, "Failed to process form")
	}

	if err := c.Service.Upsert(ctx.Request().Context(), toBeSavedEntity); err != nil {
		log.Default().Error("Error saving category", "err", err)
		msg.Warning(ctx, "エラーが発生しました")
	} else {
		msg.Success(ctx, successMsg)
	}

	return c.HandleSaveResponse(true, types.RouteCategories, ctx)
}

func (c *Categories) handleFormError(ctx echo.Context, id int64, err error) error {
	switch err.(type) {
	case validator.ValidationErrors:
		if id != 0 {
			return c.DetailsPage(ctx)
		} else {
			return c.AddNewPage(ctx)
		}
	default:
		return c.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}
}
