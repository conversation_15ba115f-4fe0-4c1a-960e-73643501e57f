package handlers

import (
	"github.com/labstack/echo/v4"
	"loms/framework"
	"loms/framework/context"
	"loms/framework/log"
	"loms/framework/page"
	"loms/pkg/templates/pages"
	"net/http"
)

type Error struct {
	*framework.TemplateRenderer
}

func (e *Error) Page(err error, ctx echo.Context) {
	if ctx.Response().Committed || context.IsCanceledError(err) {
		return
	}

	// Determine the error status code
	code := http.StatusInternalServerError
	if he, ok := err.(*echo.HTTPError); ok {
		code = he.Code
	}

	// Log the error
	logger := log.Ctx(ctx)
	switch {
	case code >= 500:
		logger.Error(err.Error())
	case code >= 400:
		logger.Warn(err.Error())
	}

	// Render the error page
	p := page.New(ctx)
	p.Title = http.StatusText(code)
	p.StatusCode = code
	p.UP.Request.Enabled = false
	p.TemplComponent = pages.Error()

	if err = e.RenderPage(ctx, p); err != nil {
		log.Ctx(ctx).Error("failed to render error page",
			"error", err,
		)
	}
}
