package handlers

const (
	successMsg = "作成しました"
)

/*
import (
	views "loms/pkg/templates/pages/locations"
	"net/http"

	"loms/framework"
	"loms/framework/form"
	"loms/framework/middleware"
	"loms/framework/msg"
	"loms/framework/page"
	"loms/pkg/services"
	"loms/pkg/types"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)



type Locations struct {
	Service *services.LocationCrudService
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(Locations))
}

func (l *Locations) Init(c *services.Container) error {
	l.TemplateRenderer = c.TemplateRenderer
	l.Service = c.LocationCrudService
	return nil
}

func (l *Locations) Routes(g *echo.Group) {
	group := g.Group("/locations", middleware.RequireAuthentication())
	group.GET("", l.ListPage).Name = types.RouteLocations
	group.GET("/details/:id", l.DetailsPage).Name = types.RouteLocationDetails
	group.GET("/add", l.AddNewPage).Name = types.RouteLocationAddNew
	group.POST("/update", l.Save).Name = types.RouteLocationUpdate
	group.GET("/import", l.CsvImportPage).Name = types.RouteLocationImport
	group.POST("/upload", l.CsvUpload).Name = types.RouteLocationImportPost
}

func (l *Locations) ListPage(ctx echo.Context) error {
	input := new(types.PageRequest)
	if err := ctx.Bind(input); err != nil {
		return l.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}

	entities, err := l.Service.FindAllPageable(ctx.Request().Context(), types.NewPageRequestOf(input))
	if err != nil {
		return l.handleError(ctx, http.StatusInternalServerError, "Error finding all pageable locations", err)
	}

	data := types.ToLocationFormPage(entities)
	p := page.New(ctx).View(views.Index(data)).Titled("積地・届け先")
	return l.RenderPage(ctx, p)
}

func (l *Locations) DetailsPage(ctx echo.Context) error {
	formValue := form.Get[types.LocationForm](ctx)
	var data *types.LocationForm
	if formValue.IsSubmitted() {
		data = formValue
	} else {
		var id int64
		if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
			return l.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
		}
		entity, err := l.Service.FindByID(ctx.Request().Context(), id)
		if err != nil {
			return l.handleError(ctx, http.StatusInternalServerError, "Error finding location by ID", err)
		}
		data = types.FromDomainLocation(entity)
	}
	data.IsEdit = true
	p := page.New(ctx).View(views.LocationForm(data)).Titled("積地・届け先 詳細")
	return l.RenderPage(ctx, p)
}

func (l *Locations) AddNewPage(ctx echo.Context) error {
	formValue := form.Get[types.LocationForm](ctx)
	p := page.New(ctx).View(views.LocationForm(formValue)).Titled("積地・届け先 新規登録")
	return l.RenderPage(ctx, p)
}

func (l *Locations) Save(ctx echo.Context) error {
	var formValue types.LocationForm

	err := form.Submit(ctx, &formValue)

	if err != nil {
		return l.handleFormError(ctx, formValue.ID, err)
	}

	toBeSavedLocation, err := formValue.ToDomainLocation()
	if err != nil {
		return l.handleError(ctx, http.StatusInternalServerError, "Error converting form to domain location", err)
	}

	if err := l.Service.Upsert(ctx.Request().Context(), toBeSavedLocation); err != nil {
		return l.handleError(ctx, http.StatusInternalServerError, "Error upserting location", err)
	}

	msg.Success(ctx, successMsg)

	return l.HandleSaveResponse(true, types.RouteLocations, ctx)
}

func (v *Locations) handleFormError(ctx echo.Context, id int64, err error) error {
	switch err.(type) {
	case validator.ValidationErrors:
		if id != 0 {
			return v.DetailsPage(ctx)
		} else {
			return v.AddNewPage(ctx)
		}
	default:
		return v.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}
}
*/
