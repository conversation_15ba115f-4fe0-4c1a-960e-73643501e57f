package api

import (
	"github.com/labstack/echo/v4"
	"loms/pkg/services"
	"loms/pkg/types"
	"net/http"
)

type Solutions struct {
	service *services.SolutionService
}

func NewSolutions(service *services.SolutionService) *Solutions {
	return &Solutions{service: service}
}

func (s *Solutions) Save(c echo.Context) error {
	input := new(types.SolutionSaveInput)
	if err := c.Bind(input); err != nil {
		return c.String(http.StatusBadRequest, "bad request")
	}
	if err := c.Validate(input); err != nil {
		return err
	}

	updated, err := s.service.Save(c.Request().Context(), input)
	if err != nil {
		return err
	}
	return c.JSON(http.StatusOK, types.SolutionSaveOutput{ID: updated.ID})
}
