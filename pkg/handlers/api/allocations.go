package api

import (
	"github.com/labstack/echo/v4"
	"loms/pkg/services"
	"loms/pkg/types"
	"net/http"
)

type (
	Allocations struct {
		service *services.AllocationRunService
	}
)

func NewAllocations(service *services.AllocationRunService) *Allocations {
	return &Allocations{service: service}
}

func (a *Allocations) Save(c echo.Context) error {
	input := new(types.AllocationSaveInput)
	if err := c.Bind(input); err != nil {
		return c.String(http.StatusBadRequest, "bad request")
	}

	if err := c.Validate(input); err != nil {
		return err
	}

	allocationRunUpdated, err := a.service.Save(c.Request().Context(), *input)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, allocationRunUpdated)
}

func (a *Allocations) Update(c echo.Context) error {
	input := new(types.AllocationUpdateInput)
	if err := c.Bind(input); err != nil {
		return c.String(http.StatusBadRequest, "bad request")
	}

	if err := c.Validate(input); err != nil {
		return err
	}

	allocationRunUpdated, err := a.service.Update(c.Request().Context(), input)
	if err != nil {
		return err
	}

	return c.JSON(http.StatusOK, allocationRunUpdated)
}
