package handlers

import (
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"loms/framework"
	"loms/framework/form"
	"loms/framework/middleware"
	"loms/framework/msg"
	"loms/framework/page"
	"loms/pkg/services"
	views "loms/pkg/templates/pages/pickups"
	"loms/pkg/types"
	"net/http"
)

type Pickups struct {
	Service *services.PickupCrudService
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(Pickups))
}

func (p *Pickups) Init(container *services.Container) error {
	p.TemplateRenderer = container.TemplateRenderer
	p.Service = container.PickupCrudService
	return nil
}

func (p *Pickups) Routes(g *echo.Group) {
	group := g.Group("/pickups", middleware.RequireAuthentication())
	group.GET("", p.ListPage).Name = types.RoutePickups
	group.GET("/details/:id", p.DetailsPage).Name = types.RoutePickupsDetails
	group.GET("/add", p.AddNewPage).Name = types.RoutePickupsAddNew
	group.POST("/update", p.Save).Name = types.RoutePickupsUpdate
}

func (p *Pickups) ListPage(ctx echo.Context) error {
	input := new(types.PageRequest)
	if err := ctx.Bind(input); err != nil {
		return p.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}

	entities, err := p.Service.FindAllPageable(ctx.Request().Context(), types.NewPageRequestOf(input))
	if err != nil {
		return p.handleError(ctx, http.StatusInternalServerError, "Error finding all pageable locations", err)
	}

	data := types.ToCoordinatesFromPage(entities)
	view := page.New(ctx).View(views.Index(data)).Titled("積地・届け先")
	return p.RenderPage(ctx, view)
}

func (p *Pickups) DetailsPage(ctx echo.Context) error {
	formValue := form.Get[types.CoordinatesUpdateForm](ctx)
	var data *types.CoordinatesUpdateForm
	if formValue.IsSubmitted() {
		data = formValue
	} else {
		var id int64
		if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
			return p.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
		}
		entity, err := p.Service.FindByID(ctx.Request().Context(), id)
		if err != nil {
			return p.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
		}
		data = types.FromDomainPickup(entity)
	}
	data.IsEdit = true

	view := page.New(ctx).View(views.PickupsForm(data)).Titled("乗務員 詳細")
	return p.RenderPage(ctx, view)
}

func (p *Pickups) AddNewPage(ctx echo.Context) error {
	data := form.Get[types.CoordinatesUpdateForm](ctx)

	view := page.New(ctx).View(views.PickupsForm(data)).Titled("乗務員 新規登録")
	return p.RenderPage(ctx, view)
}

func (p *Pickups) Save(ctx echo.Context) error {
	var formValue types.CoordinatesUpdateForm

	err := form.Submit(ctx, &formValue)

	switch err.(type) {
	case nil:
	case validator.ValidationErrors:
		if formValue.ID != 0 {
			return p.DetailsPage(ctx)
		} else {
			return p.AddNewPage(ctx)
		}
	default:
		return p.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}

	toBeSavedEntity, err := formValue.ToDomainPickup()
	if err != nil {
		return p.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}

	if err := p.Service.Upsert(ctx.Request().Context(), toBeSavedEntity); err != nil {
		msg.Warning(ctx, "エラーが発生しました")
	} else {
		msg.Success(ctx, "作成しました")
	}

	return p.HandleSaveResponse(true, types.RouteDrivers, ctx)
}
