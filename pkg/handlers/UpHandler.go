package handlers

import (
	"loms/framework/log"
	"loms/framework/redirect"
	"loms/framework/unpoly"

	"github.com/labstack/echo/v4"
)

type (
	UpHandler struct {
		AuthChangeEvent string
	}
)

// NewUpHandler creates a new UpHandler with default values
func NewUpHandler() *UpHandler {
	return &UpHandler{
		AuthChangeEvent: `[{"type": "auth:change"}]`,
	}
}

func (u *UpHandler) IsSubmittedFromModal(ctx echo.Context) bool {
	return unpoly.GetRequest(ctx).Mode == "modal"
}

func (u *UpHandler) RenderNothing(ctx echo.Context, dismissLayer bool) error {
	response := ctx.Response()
	response.Header().Set(unpoly.HeaderTarget, ":none")
	if dismissLayer {
		response.Header().Set(unpoly.HeaderDismissLayer, "1")
	}
	return ctx.NoContent(200)
}

func (u *UpHandler) UP(header string, ctx echo.Context) string {
	return ctx.Request().Header.Get(header)
}

func (u *UpHandler) IsUnpolyRequest(ctx echo.Context) bool {
	return unpoly.GetRequest(ctx).Enabled
}

func (u *UpHandler) SendUnpolyEvents(events string, ctx echo.Context) error {
	unpoly.Response{Events: events}.Apply(ctx)
	return nil
}

func (u *UpHandler) HandleSaveResponse(dismissLayer bool, redirectPath string, ctx echo.Context) error {
	if u.IsSubmittedFromModal(ctx) {
		return u.RenderNothing(ctx, dismissLayer)
	}
	return redirect.New(ctx).Route(redirectPath).Go()
}

func (u *UpHandler) handleError(ctx echo.Context, statusCode int, message string, err error) error {
	log.Default().Error("%s: %v", message, err)
	return ctx.String(statusCode, message)
}
