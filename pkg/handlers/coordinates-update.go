package handlers

import (
	"encoding/csv"
	"errors"
	"fmt"
	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"io"
	"loms/framework"
	"loms/framework/middleware"
	"loms/framework/page"
	"loms/pkg/services"
	views "loms/pkg/templates/pages/pickups"
	"loms/pkg/types"
	"mime/multipart"
	"net/http"
	"strconv"
)

const (
	importSuccessMsg = "CSVインポートが成功しました。距離マトリックスの更新が進行中です"
)

type CoordinatesUpdate struct {
	Service                  *services.PickupCrudService
	CoordinatesUpdateService *services.CoordinatesUpdateService
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(CoordinatesUpdate))
}

func (cu *CoordinatesUpdate) Init(container *services.Container) error {
	cu.TemplateRenderer = container.TemplateRenderer
	cu.Service = container.PickupCrudService
	cu.CoordinatesUpdateService = container.CoordinatesUpdateService
	return nil
}

func (cu *CoordinatesUpdate) Routes(g *echo.Group) {
	group := g.Group("/coordinates-update-import", middleware.RequireAuthentication())
	group.GET("", cu.CsvImportPage).Name = types.RouteCoordinatesUpdateImport
	group.POST("", cu.CsvUpload).Name = types.RouteCoordinatesUpdateImportPost
}

func (cu *CoordinatesUpdate) CsvImportPage(ctx echo.Context) error {
	input := new(types.LocationImportInput)
	if err := ctx.Bind(input); err != nil {
		return ctx.String(http.StatusBadRequest, badRequestMsg)
	}
	view := page.New(ctx).View(views.Import(input.Message, []string{})).Titled("緯度経度を更新 CSV インポート")
	return cu.RenderPage(ctx, view)
}

func (cu *CoordinatesUpdate) CsvUpload(ctx echo.Context) error {
	if err := ctx.Request().ParseMultipartForm(maxMemory); err != nil {
		return ctx.String(http.StatusBadRequest, parseFormErrMsg)
	}

	file, _, err := ctx.Request().FormFile("file")
	if err != nil {
		return ctx.String(http.StatusBadRequest, retrieveFileErrMsg)
	}
	defer file.Close()

	locationVMs, err := readCSV(file)
	if err != nil {
		return ctx.String(http.StatusInternalServerError, readCSVErrMsg)
	}

	validationErrors := cu.validate(locationVMs, ctx)
	if len(validationErrors) > 0 {
		view := page.New(ctx).View(views.Import("", validationErrors)).Titled("積地・届け先 新規登録")
		return cu.RenderPage(ctx, view)
	}

	if len(locationVMs) == 0 {
		return ctx.JSON(http.StatusOK, map[string]string{"message": "No data to import"})
	}

	if locationVMs[0].Type == 0 {
		if _, err := cu.CoordinatesUpdateService.PickupNameAddressCoordinatesBulkUpload(ctx.Request().Context(), types.MapToPickupLocations(locationVMs)); err != nil {
			if errors.Is(err, types.ErrDistanceMatrixUpdateInProgress) {
				return ctx.JSON(http.StatusBadRequest, map[string]string{"message": importInProgressMsg})
			}
			return ctx.String(http.StatusInternalServerError, uploadErrMsg)
		}
	} else {
		if _, err := cu.CoordinatesUpdateService.DestinationNameAddressCoordinatesBulkUpload(ctx.Request().Context(), types.MapToDestinationEntities(locationVMs)); err != nil {
			if errors.Is(err, types.ErrDistanceMatrixUpdateInProgress) {
				return ctx.JSON(http.StatusBadRequest, map[string]string{"message": importInProgressMsg})
			}
			return ctx.String(http.StatusInternalServerError, uploadErrMsg)
		}
	}

	return ctx.JSON(http.StatusOK, map[string]string{"message": importSuccessMsg})
}

func (cu *CoordinatesUpdate) validate(locations []types.CoordinatesUpdateForm, ctx echo.Context) []string {
	/*var errs []string

	toBeValidated := filterValidLocations(locations)
	duplicates := findDuplicates(toBeValidated)
	if len(duplicates) > 0 {
		for _, dup := range duplicates {
			errs = append(errs, fmt.Sprintf("%v duplicated in rows %v", dup.first, dup.second))
		}
		return errs
	}

	locationsByCoordinates, _ := cu.Service.FindAllWithFilters(ctx.Request().Context(), toBeValidated)
	for _, loc := range locations {
		if loc.LatLng != "" && locationExists(loc, locationsByCoordinates) {
			errs = append(errs, fmt.Sprintf("Location exists in db: lat_lon: %v description: %v", loc.LatLng, loc.Name))
		}
	}

	if len(errs) > 0 {
		return errs
	}*/

	return validateFields(locations, ctx)
}

func filterValidLocations(locations []types.CoordinatesUpdateForm) []types.CoordinatesUpdateForm {
	var validLocations []types.CoordinatesUpdateForm
	for _, loc := range locations {
		if loc.LatLng != "" {
			validLocations = append(validLocations, loc)
		}
	}
	return validLocations
}

func validateFields(locations []types.CoordinatesUpdateForm, ctx echo.Context) []string {
	var validationErrors []string
	for i, loc := range locations {
		if err := ctx.Validate(loc); err != nil {
			fieldErrors := getErrorMessages(err)
			validationErrors = append(validationErrors, fmt.Sprintf("Row %d: %s", i+1, fieldErrors))
		}
	}
	return validationErrors
}

func locationExists(loc types.CoordinatesUpdateForm, coordinates []types.PickupEntity) bool {
	for _, entity := range coordinates {
		if loc.LatLng == entity.Location.LatLng || loc.Name == entity.Nm {
			return true
		}
	}
	return false
}

func getErrorMessages(err error) []FieldError {
	var ves validator.ValidationErrors
	if !errors.As(err, &ves) {
		return nil
	}

	var errs []FieldError
	for _, ve := range ves {
		message := getErrorMessage(ve)
		errs = append(errs, FieldError{Field: ve.Field(), Error: message})
	}

	return errs
}

func getErrorMessage(ve validator.FieldError) string {
	switch ve.Tag() {
	case "required":
		return "必須です。"
	case "email":
		return "有効なメールアドレスを入力してください。"
	case "eqfield":
		return "パスワードが一致しません。"
	case "gte":
		return fmt.Sprintf("%v 以上である必要があります。", ve.Param())
	default:
		return "無効な値です。"
	}
}

func findDuplicates(locations []types.CoordinatesUpdateForm) []struct {
	first  string
	second []int
} {
	locationMap := make(map[string][]int)
	for i, loc := range locations {
		locationMap[loc.LatLng] = append(locationMap[loc.LatLng], i)
	}

	var duplicates []struct {
		first  string
		second []int
	}
	for key, indices := range locationMap {
		if len(indices) > 1 {
			duplicates = append(duplicates, struct {
				first  string
				second []int
			}{first: key, second: indices})
		}
	}
	return duplicates
}

func readCSV(file multipart.File) ([]types.CoordinatesUpdateForm, error) {
	reader := csv.NewReader(file)

	// Read the header to skip it
	if _, err := reader.Read(); err != nil {
		return nil, fmt.Errorf("failed to read CSV header: %w", err)
	}

	var locationVMS []types.CoordinatesUpdateForm
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("failed to read CSV record: %w", err)
		}

		location := toLocationVM(record)
		locationVMS = append(locationVMS, location)
	}

	return locationVMS, nil
}

func toLocationVM(record []string) types.CoordinatesUpdateForm {
	id, _ := strconv.ParseInt(record[0], 10, 64)
	locationType, _ := strconv.Atoi(record[1])
	parentId, _ := strconv.ParseInt(record[2], 10, 64)
	prefId, err := strconv.ParseInt(record[5], 10, 64)
	if err != nil {
		prefId = 0
	}

	return types.CoordinatesUpdateForm{
		ID:         parentId,
		Type:       locationType,
		LocationID: id,
		Name:       record[3],
		Zip:        record[4],
		PrefID:     prefId,
		CityCD:     record[6],
		Add1:       record[7],
		Add1Kana:   record[8],
		Add2:       record[9],
		Add2Kana:   record[10],
		Add3:       record[11],
		Add3Kana:   record[12],
		LatLng:     record[15],
	}
}

type FieldError struct {
	Field string
	Error string
}

func (f FieldError) String() string {
	return fmt.Sprintf("%s: %s", f.Field, f.Error)
}
