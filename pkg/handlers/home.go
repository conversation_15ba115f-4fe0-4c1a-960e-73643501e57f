package handlers

import (
	"loms/framework"
	"loms/framework/middleware"
	"loms/framework/redirect"
	"loms/pkg/services"
	"loms/pkg/types"

	"github.com/labstack/echo/v4"
)

type Home struct {
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(Home))
}

func (h *Home) Init(c *services.Container) error {
	h.TemplateRenderer = c.TemplateRenderer
	return nil
}

func (h *Home) Routes(g *echo.Group) {
	g.GET("/", h.Get, middleware.RequireAuthentication()).Name = types.RouteHome
}

func (h *Home) Get(ctx echo.Context) error {
	return redirect.New(ctx).Route(types.RouteSolution).Go()
}
