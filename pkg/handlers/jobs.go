package handlers

import (
	"loms/framework"
	"loms/framework/middleware"
	"loms/framework/page"
	"loms/pkg/services"
	views "loms/pkg/templates/pages/jobs"
	"loms/pkg/types"
	"net/http"
	"slices"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/samber/lo"
)

type Jobs struct {
	Service *services.JobCrudService
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(Jobs))
}

func (j *Jobs) Init(c *services.Container) error {
	j.TemplateRenderer = c.TemplateRenderer
	j.Service = c.JobCrudService
	return nil
}

func (j *Jobs) Routes(g *echo.Group) {
	group := g.Group("/orders", middleware.RequireAuthentication())
	group.GET("", j.ListPage).Name = types.RouteJobs
	group.GET("/by-date", j.ListByDatePage).Name = types.RouteJobsByDate
	//group.GET("/details/:id", j.DetailsPage).Name = types.RouteJobDetails
	//group.GET("/add", j.AddNewPage).Name = types.RouteJobAddNew
	//group.POST("/update", j.Save).Name = types.RouteJobUpdate
	//group.GET("/import", j.CsvImportPage).Name = types.RouteJobImport
	//group.POST("/upload", j.CsvUpload).Name = types.RouteJobImportPost
}

func (j *Jobs) ListByDatePage(ctx echo.Context) error {
	input := new(types.JobIndexData)
	if err := ctx.Bind(input); err != nil {
		return j.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}

	selectedDate := GetSelectedDate(input.Date)
	entities, err := j.Service.GetJobsByFulfillDate(ctx.Request().Context(), selectedDate)
	if err != nil {
		return j.handleError(ctx, http.StatusInternalServerError, "Error finding all pageable jobs", err)
	}

	slices.SortFunc(entities, func(a, b types.JobDto) int {
		return strings.Compare(b.Item.Name, a.Item.Name)
	})
	input.Date = types.DateYmdToDateStr(selectedDate)
	p := page.New(ctx).View(views.ByDate(entities, input)).Titled("受注一覧")
	return j.RenderPage(ctx, p)
}

func (j *Jobs) ListPage(ctx echo.Context) error {
	input := new(types.PageRequest)
	if err := ctx.Bind(input); err != nil {
		return j.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}

	entities, err := j.Service.FindAllPageable(ctx.Request().Context(), types.NewPageRequestOf(input))
	if err != nil {
		return j.handleError(ctx, http.StatusInternalServerError, "Error finding all pageable jobs", err)
	}

	data := types.ToJobFormPage(entities)
	p := page.New(ctx).View(views.Index(data)).Titled("受注")
	return j.RenderPage(ctx, p)
}

/*func (j *Jobs) DetailsPage(ctx echo.Context) error {
	formValue := form.Get[types.JobForm](ctx)
	var data *types.JobForm
	if formValue.IsSubmitted() {
		data = formValue
	} else {
		var id int64
		if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
			return j.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
		}
		entity, err := j.Service.FindByID(ctx.Request().Context(), id)
		if err != nil {
			return j.handleError(ctx, http.StatusInternalServerError, "Error finding job by ID", err)
		}
		data = types.FromDomainJob(entity)
	}
	data.IsEdit = true

	pickups, deliveries, err := j.Service.FindAllPickupsDeliveries(ctx.Request().Context())
	if err != nil {
		return err
	}

	pickupOptions := LocationsToOptions(pickups, data.PickupID)
	deliveriesOptions := LocationsToOptions(deliveries, data.DeliveryID)
	categories, err := j.getCategoryOptions(ctx, data.MaterialCategoryID)
	if err != nil {
		return err
	}

	p := page.New(ctx).View(views.JobForm(data, pickupOptions, deliveriesOptions, categories)).Titled("受注 詳細")
	return j.RenderPage(ctx, p)
}*/

/*func (j *Jobs) AddNewPage(ctx echo.Context) error {
	formValue := form.Get[types.JobForm](ctx)

	pickups, deliveries, err := j.Service.FindAllPickupsDeliveries(ctx.Request().Context())
	if err != nil {
		return err
	}

	pickupOptions := LocationsToOptions(pickups, formValue.PickupID)
	deliveriesOptions := LocationsToOptions(deliveries, formValue.DeliveryID)
	categories, err := j.getCategoryOptions(ctx, formValue.MaterialCategoryID)
	if err != nil {
		return err
	}

	p := page.New(ctx).View(views.JobForm(formValue, pickupOptions, deliveriesOptions, categories)).Titled("受注 新規登録")
	return j.RenderPage(ctx, p)
}*/

/*func (j *Jobs) Save(ctx echo.Context) error {
	var formValue types.JobForm

	err := form.Submit(ctx, &formValue)

	if err != nil {
		return j.handleFormError(ctx, formValue.ID, err)
	}

	toBeSavedJob, err := formValue.ToDomainJob()
	if err != nil {
		return j.handleError(ctx, http.StatusInternalServerError, "Error converting form to domain job", err)
	}

	if err := j.Service.Upsert(ctx.Request().Context(), toBeSavedJob); err != nil {
		return j.handleError(ctx, http.StatusInternalServerError, "Error upserting Job", err)
	}

	msg.Success(ctx, successMsg)

	return j.HandleSaveResponse(true, types.RouteJobs, ctx)
}

func (j *Jobs) handleFormError(ctx echo.Context, id int64, err error) error {
	switch err.(type) {
	case validator.ValidationErrors:
		if id != 0 {
			return j.DetailsPage(ctx)
		} else {
			return j.AddNewPage(ctx)
		}
	default:
		return j.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}
}*/

func (j *Jobs) getCategoryOptions(ctx echo.Context, canHandle int64) ([]types.Option, error) {
	categories, err := j.Service.FindAllCategories(ctx.Request().Context())
	if err != nil {
		return nil, err
	}
	options := CategoriesToOptions(categories, []int64{canHandle})
	return options, nil
}

func LocationsToOptions(locations []types.LocationEntity, id int64) []types.Option {
	return lo.Map(locations, func(location types.LocationEntity, _ int) types.Option {
		return types.Option{
			ID: location.ID,
			//Value:     location.Description,
			IsChecked: id == location.ID,
		}
	})
}
