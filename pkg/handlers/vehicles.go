package handlers

import (
	"loms/framework"
	"loms/framework/form"
	"loms/framework/middleware"
	"loms/framework/msg"
	"loms/framework/page"
	"loms/pkg/services"
	views "loms/pkg/templates/pages/vehicles"
	"loms/pkg/types"
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)

type Vehicles struct {
	Service *services.VehicleCrudService
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(Vehicles))
}

func (v *Vehicles) Init(container *services.Container) error {
	v.TemplateRenderer = container.TemplateRenderer
	v.Service = container.VehicleCrudService
	return nil
}

func (v *Vehicles) Routes(g *echo.Group) {
	group := g.Group("/vehicles", middleware.RequireAuthentication())
	group.GET("", v.ListPage).Name = types.RouteVehicles
	group.GET("/details/:id", v.DetailsPage).Name = types.RouteVehiclesDetails
	group.GET("/add", v.AddNewPage).Name = types.RouteVehiclesAddNew
	group.POST("/update", v.Save).Name = types.RouteVehiclesUpdate
}

func (v *Vehicles) ListPage(ctx echo.Context) error {
	input := new(types.PageRequest)
	if err := ctx.Bind(input); err != nil {
		return v.handleError(ctx, http.StatusBadRequest, "bad request")
	}

	entities, err := v.Service.FindAllPageable(ctx.Request().Context(), types.NewPageRequestOf(input))
	if err != nil {
		return v.handleError(ctx, http.StatusInternalServerError, err.Error())
	}

	data := types.ToVehicleFormPage(entities)
	p := page.New(ctx).View(views.Index(data)).Titled("車両")
	return v.RenderPage(ctx, p)
}

func (v *Vehicles) DetailsPage(ctx echo.Context) error {
	formValue := form.Get[types.VehicleForm](ctx)
	var data *types.VehicleForm
	if formValue.IsSubmitted() {
		data = formValue
	} else {
		var id int64
		if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
			return v.handleError(ctx, http.StatusBadRequest, "bad request")
		}
		entity, err := v.Service.FindByID(ctx.Request().Context(), id)
		if err != nil {
			return v.handleError(ctx, http.StatusInternalServerError, err.Error())
		}
		data = types.FromDomainVehicle(entity)
	}
	data.IsEdit = true

	options, err := v.getOptions(ctx, data.CanHandle)
	if err != nil {
		return v.handleError(ctx, http.StatusInternalServerError, err.Error())
	}

	p := page.New(ctx).View(views.VehiclesForm(data, options)).Titled("車両 詳細")
	return v.RenderPage(ctx, p)
}

func (v *Vehicles) AddNewPage(ctx echo.Context) error {
	data := form.Get[types.VehicleForm](ctx)

	options, err := v.getOptions(ctx, data.CanHandle)
	if err != nil {
		return v.handleError(ctx, http.StatusInternalServerError, err.Error())
	}
	p := page.New(ctx).View(views.VehiclesForm(data, options)).Titled("車両 新規登録")
	return v.RenderPage(ctx, p)
}

func (v *Vehicles) getOptions(ctx echo.Context, canHandle []int64) ([]types.Option, error) {
	categories, err := v.Service.FindAllCategories(ctx.Request().Context())
	if err != nil {
		return nil, err
	}
	options := CategoriesToOptions(categories, canHandle)
	return options, nil
}

func (v *Vehicles) Save(ctx echo.Context) error {
	var formValue types.VehicleForm

	err := form.Submit(ctx, &formValue)

	if err != nil {
		return v.handleFormError(ctx, formValue, err)
	}

	toBeSavedEntity, err := formValue.ToDomainVehicle()
	if err != nil {
		return v.handleError(ctx, http.StatusInternalServerError, err.Error())
	}

	if err := v.Service.Upsert(ctx.Request().Context(), toBeSavedEntity); err != nil {
		msg.Warning(ctx, "エラーが発生しました")
	} else {
		msg.Success(ctx, "作成しました")
	}

	return v.HandleSaveResponse(true, types.RouteVehicles, ctx)
}

func (v *Vehicles) handleError(ctx echo.Context, statusCode int, message string) error {
	return ctx.String(statusCode, message)
}

func (v *Vehicles) handleFormError(ctx echo.Context, formValue types.VehicleForm, err error) error {
	switch err.(type) {
	case validator.ValidationErrors:
		if formValue.ID != 0 {
			return v.DetailsPage(ctx)
		} else {
			return v.AddNewPage(ctx)
		}
	default:
		return v.handleError(ctx, http.StatusInternalServerError, err.Error())
	}
}
