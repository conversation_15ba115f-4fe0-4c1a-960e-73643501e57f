package handlers

import (
	"github.com/labstack/echo/v4"
	middleware2 "loms/framework/middleware"
	"loms/pkg"
	"loms/pkg/handlers/api"
	"net/http"
	"net/url"
	"strings"

	"github.com/gorilla/sessions"
	echomw "github.com/labstack/echo/v4/middleware"
	"loms/config"
	"loms/pkg/services"
)

// BuildRouter builds the router
func BuildRouter(c *services.Container) error {

	c.Web.Pre(echomw.MethodOverrideWithConfig(echomw.MethodOverrideConfig{
		Getter: echomw.MethodFromForm("_method"),
	}))

	// Get the embedded static filesystem
	staticFiles, er := pkg.StaticFiles()
	if er != nil {
		c.Web.Logger.Fatal("Could not get embedded static files: ", er)
	}

	// Create a group for static files with the CacheControl middleware
	staticGroup := c.Web.Group(config.StaticPrefix, middleware2.CacheControl(c.Config.Cache.Expiration.StaticFile))
	staticGroup.GET("/*", echo.WrapHandler(StripPrefix(config.StaticPrefix, http.FileServer(http.FS(staticFiles)))))

	// Static files with proper cache control
	// funcmap.File() should be used in templates to append a cache key to the URL in order to break cache
	// after each server restart
	// c.Web.Group("", middleware.CacheControl(c.Config.Cache.Expiration.StaticFile)).
	// Static(config.StaticPrefix, config.StaticDir)

	// Non-static file route group
	g := c.Web.Group("")

	// Force HTTPS, if enabled
	if c.Config.HTTP.TLS.Enabled {
		g.Use(echomw.HTTPSRedirect())
	}

	g.Use(
		echomw.RemoveTrailingSlashWithConfig(echomw.TrailingSlashConfig{
			RedirectCode: http.StatusMovedPermanently,
		}),
		echomw.Recover(),
		echomw.Secure(),
		echomw.RequestID(),
		middleware2.SetLogger(),
		middleware2.LogRequest(),
		echomw.Gzip(),
		echomw.TimeoutWithConfig(echomw.TimeoutConfig{
			Timeout: c.Config.App.Timeout,
		}),
		middleware2.Session(sessions.NewCookieStore([]byte(c.Config.App.EncryptionKey))),
		middleware2.LoadAuthenticatedUser(c.Auth),
		middleware2.ServeCachedPage(c.TemplateRenderer),
		echomw.CSRFWithConfig(echomw.CSRFConfig{
			TokenLookup: "header:X-CSRF-Token,form:csrf",
		}),
	)

	// Error handler
	err := Error{c.TemplateRenderer}
	c.Web.HTTPErrorHandler = err.Page

	// Initialize and register all handlers
	for _, h := range GetHandlers() {
		if err := h.Init(c); err != nil {
			return err
		}

		h.Routes(g)
	}

	apiG := c.Web.Group("/api/v1")

	allocations := api.NewAllocations(c.AllocationRunService)
	allocationsApiG := apiG.Group("/allocations")
	allocationsApiG.POST("", allocations.Save)
	allocationsApiG.PUT("/:id", allocations.Update)

	solutions := api.NewSolutions(c.SolutionService)
	solutionsApiG := apiG.Group("/solutions")
	solutionsApiG.POST("", solutions.Save)

	return nil
}

// StripPrefix Handler that strips the prefix and serves the embedded files
func StripPrefix(prefix string, h http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		p := strings.TrimPrefix(r.URL.Path, "/"+prefix)
		if len(p) < len(r.URL.Path) {
			r2 := new(http.Request)
			*r2 = *r
			r2.URL = new(url.URL)
			*r2.URL = *r.URL
			r2.URL.Path = p
			if r.URL.RawPath != "" {
				r2.URL.RawPath = strings.TrimPrefix(r.URL.RawPath, prefix)
			}
			h.ServeHTTP(w, r2)
		} else {
			http.NotFound(w, r)
		}
	})
}
