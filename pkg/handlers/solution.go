package handlers

import (
	"errors"
	"fmt"
	"github.com/go-playground/validator/v10"
	"github.com/samber/lo"
	"loms/framework"
	"loms/framework/form"
	"loms/framework/log"
	"loms/framework/middleware"
	"loms/framework/msg"
	"loms/framework/page"
	"loms/framework/redirect"
	"loms/framework/unpoly"
	"loms/pkg/services"
	"loms/pkg/templates/pages/solution"
	"loms/pkg/types"
	"net/http"
	"net/url"
	"time"

	"github.com/labstack/echo/v4"
)

const (
	dateFormat             = types.ApplicationDbDateFormat
	internalServerErrorMsg = "internal server error"
	dateRequiredMsg        = "日付が必要です"
	invalidDateFormatMsg   = "無効な日付形式です"
	solveFailedMsg         = "解決策の計算に失敗しました"
	failedToGetRunsMsg     = "failed to get allocation runs"
	failedToFindSolutions  = "failed to find feasible solutions"
)

type Solution struct {
	solutionService *services.SolutionService
	Map             types.SolutionMapInput
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(&Solution{})
}

func (s *Solution) Init(c *services.Container) error {

	s.TemplateRenderer = c.TemplateRenderer
	s.solutionService = c.SolutionService
	s.Map = types.SolutionMapInput{
		ApiUrl:         c.Config.App.NavitimeApiUrl,
		ToBeLoadedFrom: c.Config.App.NavitimeToBeLoadedFrom,
		CustomerId:     c.Config.App.NavitimeCustomerId,
		Signature:      c.Config.App.NavitimeSignature,
		RequestCode:    c.Config.App.NavitimeRequestCode,
	}
	return nil
}

func (s *Solution) Routes(g *echo.Group) {
	group := g.Group("/solution", middleware.RequireAuthentication())
	group.GET("", s.GetIndex).Name = types.RouteSolution
	group.GET("/run", s.GetRun).Name = types.RouteRunDetails
	group.GET("/map", s.GetMap).Name = types.RouteMap
	group.POST("/solve", s.Solve).Name = types.RouteSolve
	group.POST("/update-solution", s.UpdateSolution).Name = types.UpdateSolution
	group.DELETE("/delete/:id", s.Delete).Name = types.RouteSolutionDelete
}

func (s *Solution) GetIndex(ctx echo.Context) error {
	input := new(types.SolutionIndexInput)
	if err := ctx.Bind(input); err != nil {
		log.Default().Error("Error binding input", "err", err)
		return ctx.String(http.StatusBadRequest, badRequestMsg)
	}

	selectedRunId := input.RunId
	upHeaderValidate := s.UP(unpoly.HeaderValidate, ctx)
	if upHeaderValidate == "date" {
		selectedRunId = 0
	}

	selectedDate := GetSelectedDate(input.Date)

	selectedSolutionId := s.getSelectedSolutionId(upHeaderValidate, input.SolutionId)
	if selectedSolutionId != 0 {
		return s.renderSolutionDetails(ctx, selectedSolutionId, nil)
	}

	data, err := s.prepareIndexData(ctx, selectedDate, selectedRunId, selectedSolutionId)
	if err != nil {
		return err
	}

	return s.renderIndexPage(ctx, data, upHeaderValidate)
}

func GetSelectedDate(dateStr string) int64 {
	if dateStr == "" {
		dateStr = time.Now().Format("20060102")
	}
	parsedDate, err := types.DateStrToYmd(dateStr)
	if err != nil {
		return 0
	}
	return parsedDate
}

func (s *Solution) getSelectedSolutionId(upHeaderValidate string, solutionId int64) int64 {
	if upHeaderValidate != "date" && upHeaderValidate != "runId" && solutionId != 0 {
		return solutionId
	}
	return 0
}

func (s *Solution) prepareIndexData(ctx echo.Context, selectedDate int64, selectedRunId int64, selectedSolutionId int64) (*types.SolutionIndexData, error) {
	allocationRuns, err := s.solutionService.GetAllocationRunsByDate(ctx.Request().Context(), selectedDate)
	if err != nil {
		log.Default().Error("Error getting allocation runs", "err", err)
		return nil, ctx.String(http.StatusInternalServerError, failedToGetRunsMsg)
	}

	solutions, err := s.getSolutions(ctx, selectedRunId)
	if err != nil {
		return nil, err
	}

	return &types.SolutionIndexData{
		Date:       fmt.Sprintf("%d", selectedDate),
		RunId:      selectedRunId,
		SolutionId: selectedSolutionId,
		Runs: lo.Map(allocationRuns, func(r types.AllocationRunEntity, _ int) types.RunDropDownData {
			historyIncluded := ""
			if r.WithHistory {
				historyIncluded = " - 過去のデータを考慮"
			}
			evenAllocation := ""
			if r.WithEvenAllocation {
				evenAllocation = " - 均等配車"
			}
			return types.RunDropDownData{RunID: r.ID, Text: fmt.Sprintf("%d%s%s", r.ID, historyIncluded, evenAllocation)}
		}),
		Solutions: solutions,
	}, nil
}

func (s *Solution) getSolutions(ctx echo.Context, selectedRunId int64) ([]types.SolutionDropDownData, error) {
	if selectedRunId == 0 {
		return []types.SolutionDropDownData{}, nil
	}

	solutions, err := s.solutionService.FindAllFeasible(ctx.Request().Context(), selectedRunId)
	if err != nil {
		log.Default().Error("Error finding feasible solutions", "err", err)
		return nil, ctx.String(http.StatusInternalServerError, failedToFindSolutions)
	}
	return solutions, nil
}

func (s *Solution) renderIndexPage(ctx echo.Context, data *types.SolutionIndexData, upHeaderValidate string) error {
	p := page.New(ctx).View(solution.Index(data)).Titled("車両、ドライバー　配車")
	if upHeaderValidate != "" {
		p = page.New(ctx).View(solution.Validate(data, upHeaderValidate))
		return s.RenderPartial(ctx, p)
	}
	return s.RenderPage(ctx, p)
}

func (s *Solution) renderSolutionDetails(c echo.Context, solutionId int64, messages []string) error {
	solutionVM, err := s.solutionService.FindById(c.Request().Context(), solutionId)
	if err != nil {
		log.Default().Error("Error finding solution by ID", "err", err)
		return c.String(http.StatusInternalServerError, internalServerErrorMsg)
	}
	p := page.New(c).View(solution.SolutionPage(&types.SolutionDetailsData{
		Solution:      solutionVM,
		Vehicle:       0,
		IsInclude:     true,
		ErrorMessages: messages,
	}))
	return s.RenderPartial(c, p)
}

func (s *Solution) GetRun(ctx echo.Context) error {
	var date string
	if err := echo.PathParamsBinder(ctx).String("date", &date).BindError(); err != nil {
		return s.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}

	if date == "" {
		date = time.Now().Format(dateFormat)
	}

	solverInProgress, err := s.solutionService.IsThereAnySolverInProgress(ctx.Request().Context())
	if err != nil {
		log.Default().Error("Error checking solver progress", "err", err)
	}

	title := "今すぐ配車"
	if solverInProgress {
		title = "配車中"
	}

	data := &types.SolutionRunData{
		Date:             date,
		SolverInProgress: solverInProgress,
		Title:            title,
	}

	p := page.New(ctx).View(solution.Run(data))
	return s.RenderPartial(ctx, p)
}

func (s *Solution) GetMap(ctx echo.Context) error {
	p := page.New(ctx).View(solution.Map(&s.Map))
	return s.RenderPartial(ctx, p)
}

func (s *Solution) Solve(ctx echo.Context) error {
	var date string
	if err := echo.FormFieldBinder(ctx).String("date", &date).BindError(); err != nil {
		return s.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}

	var withHistory bool
	if err := echo.FormFieldBinder(ctx).Bool("withHistory", &withHistory).BindError(); err != nil {
		withHistory = false
	}

	var withEvenAllocation bool
	if err := echo.FormFieldBinder(ctx).Bool("withEvenAllocation", &withEvenAllocation).BindError(); err != nil {
		withEvenAllocation = false
	}

	if date == "" {
		return ctx.String(http.StatusBadRequest, dateRequiredMsg)
	}

	dateSelected, err := types.DateStrToYmd(date)
	if err != nil {
		return ctx.String(http.StatusBadRequest, invalidDateFormatMsg)
	}

	if err := s.processSolution(ctx, dateSelected, withHistory, withEvenAllocation); err != nil {
		return err
	}

	msg.Info(ctx, "解決策が計算されています。実行詳細ページにリダイレクトしています...")
	return s.redirectToRunDetails(ctx, dateSelected)
}

func (s *Solution) UpdateSolution(ctx echo.Context) error {
	var formValue types.UpdateSolutionForm
	err := form.Submit(ctx, &formValue)

	if err != nil {
		return s.handleFormError(ctx, err)
	}

	response, err := s.solutionService.UpdateSolution(ctx.Request().Context(), formValue)
	if err != nil {
		return err
	}

	var id int64
	var messages []string
	if !response.IsValid {
		id = formValue.SolutionID
		msg.Danger(ctx, "ハード制約に失敗しました: "+response.Messages[0])
		messages = response.Messages
	} else {
		if err := ctx.Validate(response.Solution); err != nil {
			return err
		}
		savedSolution, err := s.solutionService.Save(ctx.Request().Context(), &response.Solution)
		if err != nil {
			return err
		}
		id = savedSolution.ID
		msg.Success(ctx, "ジョブシーケンスが変更されました。")
	}
	return s.renderSolutionDetails(ctx, id, messages)
}

func (s *Solution) handleFormError(ctx echo.Context, err error) error {
	switch err.(type) {
	case validator.ValidationErrors:
		return s.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	default:
		return s.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}
}

func (s *Solution) Delete(ctx echo.Context) error {
	var id int64
	if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
		return s.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
	}

	solution, err := s.solutionService.FindById(ctx.Request().Context(), id)
	if err != nil {
		return s.handleError(ctx, http.StatusInternalServerError, "Error finding solution by ID", err)
	}
	date, err := time.Parse(dateFormat, solution.Date)

	if err := s.solutionService.Delete(ctx.Request().Context(), id); err != nil {
		return s.handleError(ctx, http.StatusInternalServerError, "Error deleting solution", err)
	}

	msg.Success(ctx, "削除しました")
	return redirect.New(ctx).Route(types.RouteSolution).Query(url.Values{
		"date":  {date.Format(dateFormat)},
		"runId": {fmt.Sprintf("%d", solution.RunId)},
	}).Go()
}

func (s *Solution) processSolution(ctx echo.Context, dateSelected int64, withHistory, withEvenAllocation bool) error {
	err := s.solutionService.SolveAndListen(ctx.Request().Context(), dateSelected, withHistory, withEvenAllocation)
	if err != nil {
		if errors.Is(err, types.ErrNoJobsFound) {
			msg.Warning(ctx, err.Error())
			return s.redirectToRunDetails(ctx, dateSelected)
		}
		log.Default().Error("Error solving and listening", "err", err)
		return ctx.String(http.StatusInternalServerError, solveFailedMsg)
	}
	return nil
}

func (s *Solution) redirectToRunDetails(ctx echo.Context, date int64) error {
	return redirect.New(ctx).Route(types.RouteRunDetails).Query(url.Values{
		"date": {types.DateYmdToDateStr(date)},
	}).Go()
}
