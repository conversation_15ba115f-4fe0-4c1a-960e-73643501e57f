package handlers

import (
	"loms/framework"
	"loms/framework/form"
	"loms/framework/middleware"
	"loms/framework/msg"
	"loms/framework/page"
	"loms/pkg/services"
	views "loms/pkg/templates/pages/drivers"
	"loms/pkg/types"
	"net/http"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/samber/lo"
)

type Drivers struct {
	Service *services.DriverCrudService
	*framework.TemplateRenderer
	*UpHandler
}

func init() {
	Register(new(Drivers))
}

func (d *Drivers) Init(container *services.Container) error {
	d.TemplateRenderer = container.TemplateRenderer
	d.Service = container.DriverCrudService
	return nil
}

func (d *Drivers) Routes(g *echo.Group) {
	group := g.Group("/drivers", middleware.RequireAuthentication())
	group.GET("", d.ListPage).Name = types.RouteDrivers
	group.GET("/details/:id", d.DetailsPage).Name = types.RouteDriversDetails
	group.GET("/add", d.AddNewPage).Name = types.RouteDriversAddNew
	group.POST("/update", d.Save).Name = types.RouteDriversUpdate
}

func (d *Drivers) ListPage(ctx echo.Context) error {
	input := new(types.PageRequest)
	if err := ctx.Bind(input); err != nil {
		return d.handleError(ctx, http.StatusBadRequest, "bad request", err)
	}

	entities, err := d.Service.FindAllPageable(ctx.Request().Context(), types.NewPageRequestOf(input))
	if err != nil {
		return d.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}

	data := types.ToDriverFormPage(entities)
	p := page.New(ctx).View(views.Index(data)).Titled("乗務員")
	return d.RenderPage(ctx, p)
}

func (d *Drivers) DetailsPage(ctx echo.Context) error {
	formValue := form.Get[types.DriverForm](ctx)
	var data *types.DriverForm
	if formValue.IsSubmitted() {
		data = formValue
	} else {
		var id int64
		if err := echo.PathParamsBinder(ctx).Int64("id", &id).BindError(); err != nil {
			return d.handleError(ctx, http.StatusBadRequest, badRequestMsg, err)
		}
		entity, err := d.Service.FindByID(ctx.Request().Context(), id)
		if err != nil {
			return d.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
		}
		data = types.FromDomainDriver(entity)
	}
	data.IsEdit = true

	options, err := d.getOptions(ctx, data.CanHandle)
	if err != nil {
		return d.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}

	p := page.New(ctx).View(views.DriversForm(data, options)).Titled("乗務員 詳細")
	return d.RenderPage(ctx, p)
}

func (d *Drivers) AddNewPage(ctx echo.Context) error {
	data := form.Get[types.DriverForm](ctx)

	options, err := d.getOptions(ctx, data.CanHandle)
	if err != nil {
		return d.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}
	p := page.New(ctx).View(views.DriversForm(data, options)).Titled("乗務員 新規登録")
	return d.RenderPage(ctx, p)
}

func (d *Drivers) getOptions(ctx echo.Context, canHandle []int64) ([]types.Option, error) {
	categories, err := d.Service.FindAllCategories(ctx.Request().Context())
	if err != nil {
		return nil, err
	}
	options := CategoriesToOptions(categories, canHandle)
	return options, nil
}

func CategoriesToOptions(categories []types.ItemEntity, canHandle []int64) []types.Option {
	return lo.Map(categories, func(cat types.ItemEntity, _ int) types.Option {
		return types.Option{
			ID:        cat.ID,
			Value:     cat.Name,
			IsChecked: lo.Contains(canHandle, cat.ID),
		}
	})
}

func (d *Drivers) Save(ctx echo.Context) error {
	var formValue types.DriverForm

	err := form.Submit(ctx, &formValue)

	switch err.(type) {
	case nil:
	case validator.ValidationErrors:
		if formValue.ID != 0 {
			return d.DetailsPage(ctx)
		} else {
			return d.AddNewPage(ctx)
		}
	default:
		return d.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}

	toBeSavedEntity, err := formValue.ToDomainDriver()
	if err != nil {
		return d.handleError(ctx, http.StatusInternalServerError, err.Error(), err)
	}

	if err := d.Service.Upsert(ctx.Request().Context(), toBeSavedEntity); err != nil {
		msg.Warning(ctx, "エラーが発生しました")
	} else {
		msg.Success(ctx, "作成しました")
	}

	return d.HandleSaveResponse(true, types.RouteDrivers, ctx)
}
