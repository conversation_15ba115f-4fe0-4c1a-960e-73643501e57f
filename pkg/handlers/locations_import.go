package handlers

const (
	maxMemory           = 10 << 20 // 10 MB
	badRequestMsg       = "bad request"
	parseFormErrMsg     = "Failed to parse multipart form"
	retrieveFileErrMsg  = "Failed to retrieve file"
	readCSVErrMsg       = "Failed to read CSV file"
	uploadErrMsg        = "一括アップロードに失敗しました"
	importInProgressMsg = "インポートが進行中です"
)

/*
import (
	"encoding/csv"
	"errors"
	"fmt"
	"io"
	"loms/framework/page"
	"loms/pkg/templates/pages/locations"
	"loms/pkg/types"
	"mime/multipart"
	"net/http"
	"strconv"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)



func (l *Locations) CsvImportPage(ctx echo.Context) error {
	input := new(types.LocationImportInput)
	if err := ctx.Bind(input); err != nil {
		return ctx.String(http.StatusBadRequest, badRequestMsg)
	}
	p := page.New(ctx).View(locations.Import(input.Message, []string{})).Titled("積地・届け先 CSV インポート")
	return l.RenderPage(ctx, p)
}

func (l *Locations) CsvUpload(ctx echo.Context) error {
	if err := ctx.Request().ParseMultipartForm(maxMemory); err != nil {
		return ctx.String(http.StatusBadRequest, parseFormErrMsg)
	}

	file, _, err := ctx.Request().FormFile("file")
	if err != nil {
		return ctx.String(http.StatusBadRequest, retrieveFileErrMsg)
	}
	defer file.Close()

	locationVMs, err := readCSV(file)
	if err != nil {
		return ctx.String(http.StatusInternalServerError, readCSVErrMsg)
	}

	validationErrors := l.validate(locationVMs, ctx)
	if len(validationErrors) > 0 {
		p := page.New(ctx).View(locations.Import("", validationErrors)).Titled("積地・届け先 新規登録")
		return l.RenderPage(ctx, p)
	}

	if _, err := l.Service.BulkUpload(ctx.Request().Context(), types.MapLocations(locationVMs)); err != nil {
		if errors.Is(err, types.ErrDistanceMatrixUpdateInProgress) {
			return ctx.JSON(http.StatusBadRequest, map[string]string{"message": importInProgressMsg})
		}
		return ctx.String(http.StatusInternalServerError, uploadErrMsg)
	}

	return ctx.JSON(http.StatusOK, map[string]string{"message": importSuccessMsg})
}

func (l *Locations) validate(locations []types.LocationForm, ctx echo.Context) []string {
	var errs []string

	toBeValidated := filterValidLocations(locations)
	duplicates := findDuplicates(toBeValidated)
	if len(duplicates) > 0 {
		for _, dup := range duplicates {
			errs = append(errs, fmt.Sprintf("%v duplicated in rows %v", dup.first, dup.second))
		}
		return errs
	}

	locationsByCoordinates, _ := l.Service.FindAllWithFilters(ctx.Request().Context(), toBeValidated)
	for _, loc := range locations {
		if loc.LatLon != "" && locationExists(loc, locationsByCoordinates) {
			errs = append(errs, fmt.Sprintf("Location exists in db: lat_lon: %v description: %v", loc.LatLon, loc.Description))
		}
	}

	if len(errs) > 0 {
		return errs
	}

	return validateFields(locations, ctx)
}

func filterValidLocations(locations []types.LocationForm) []types.LocationForm {
	var validLocations []types.LocationForm
	for _, loc := range locations {
		if loc.LatLon != "" {
			validLocations = append(validLocations, loc)
		}
	}
	return validLocations
}

func validateFields(locations []types.LocationForm, ctx echo.Context) []string {
	var validationErrors []string
	for i, loc := range locations {
		if err := ctx.Validate(loc); err != nil {
			fieldErrors := getErrorMessages(err)
			validationErrors = append(validationErrors, fmt.Sprintf("Row %d: %s", i+1, fieldErrors))
		}
	}
	return validationErrors
}

func locationExists(loc types.LocationForm, coordinates []types.LocationEntity) bool {
	for _, entity := range coordinates {
		if loc.LatLon == entity.LatLon || loc.Description == entity.Description {
			return true
		}
	}
	return false
}

func getErrorMessages(err error) []FieldError {
	var ves validator.ValidationErrors
	if !errors.As(err, &ves) {
		return nil
	}

	var errs []FieldError
	for _, ve := range ves {
		message := getErrorMessage(ve)
		errs = append(errs, FieldError{Field: ve.Field(), Error: message})
	}

	return errs
}

func getErrorMessage(ve validator.FieldError) string {
	switch ve.Tag() {
	case "required":
		return "必須です。"
	case "email":
		return "有効なメールアドレスを入力してください。"
	case "eqfield":
		return "パスワードが一致しません。"
	case "gte":
		return fmt.Sprintf("%v 以上である必要があります。", ve.Param())
	default:
		return "無効な値です。"
	}
}

func findDuplicates(locations []types.LocationForm) []struct {
	first  string
	second []int
} {
	locationMap := make(map[string][]int)
	for i, loc := range locations {
		locationMap[loc.LatLon] = append(locationMap[loc.LatLon], i)
	}

	var duplicates []struct {
		first  string
		second []int
	}
	for key, indices := range locationMap {
		if len(indices) > 1 {
			duplicates = append(duplicates, struct {
				first  string
				second []int
			}{first: key, second: indices})
		}
	}
	return duplicates
}

func readCSV(file multipart.File) ([]types.LocationForm, error) {
	reader := csv.NewReader(file)

	// Read the header to skip it
	if _, err := reader.Read(); err != nil {
		return nil, fmt.Errorf("failed to read CSV header: %w", err)
	}

	var locationVMS []types.LocationForm
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("failed to read CSV record: %w", err)
		}

		location := toLocationVM(record)
		locationVMS = append(locationVMS, location)
	}

	return locationVMS, nil
}

func toLocationVM(record []string) types.LocationForm {
	romsId, _ := strconv.ParseInt(record[0], 10, 64)
	locationType, _ := strconv.Atoi(record[8])
	readyTime, _ := strconv.ParseInt(record[10], 10, 64)
	dueTime, _ := strconv.ParseInt(record[11], 10, 64)
	serviceDuration, _ := strconv.ParseInt(record[12], 10, 64)

	return types.LocationForm{
		RomsID:          romsId,
		Description:     record[1],
		Zip:             record[2],
		Add1:            record[3],
		Add2:            record[4],
		Add3:            record[5],
		Address:         record[6],
		LatLon:          record[7],
		Type:            locationType,
		IsDepot:         record[9] == "1",
		ReadyTime:       readyTime,
		DueTime:         dueTime,
		ServiceDuration: serviceDuration,
	}
}

type FieldError struct {
	Field string
	Error string
}

func (f FieldError) String() string {
	return fmt.Sprintf("%s: %s", f.Field, f.Error)
}
*/
