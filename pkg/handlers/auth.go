package handlers

import (
	"fmt"
	"loms/framework"
	"loms/framework/context"
	"loms/framework/form"
	"loms/framework/log"
	"loms/framework/middleware"
	"loms/framework/msg"
	"loms/framework/page"
	"loms/framework/redirect"
	"loms/pkg/services"
	"loms/pkg/services/auth"
	auth2 "loms/pkg/templates/pages/auth"
	"loms/pkg/types"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/uptrace/bun"
)

type (
	Auth struct {
		auth *auth.Client
		mail *framework.MailClient
		db   *bun.DB
		*framework.TemplateRenderer
		*UpHandler
	}
)

func init() {
	Register(&Auth{
		UpHandler: NewUpHandler(),
	})
}

func (h *Auth) Init(c *services.Container) error {
	h.TemplateRenderer = c.TemplateRenderer
	h.db = c.Db
	h.auth = c.Auth
	h.mail = c.Mail
	return nil
}

func (h *Auth) Routes(g *echo.Group) {
	g.GET("/logout", h.Logout, middleware.RequireAuthentication()).Name = types.RouteLogout
	g.GET("/email/verify/:token", h.VerifyEmail).Name = types.RouteVerifyEmail

	noAuth := g.Group("", middleware.RequireNoAuthentication())
	noAuth.GET("/login", h.LoginPage).Name = types.RouteLogin
	noAuth.POST("/login", h.LoginSubmit).Name = types.RouteLoginSubmit
	noAuth.GET("/register", h.RegisterPage).Name = types.RouteRegister
	noAuth.POST("/register", h.RegisterSubmit).Name = types.RouteRegisterSubmit
	noAuth.GET("/password", h.ForgotPasswordPage).Name = types.RouteForgotPassword
	noAuth.POST("/password", h.ForgotPasswordSubmit).Name = types.RouteForgotPasswordSubmit

	rGroup := noAuth.Group("/password/reset",
		middleware.LoadUser(h.db),
		middleware.LoadValidPasswordToken(h.auth),
	)
	rGroup.GET("/token/:user/:password_token/:token", h.ResetPasswordPage).Name = types.RouteResetPassword
	rGroup.POST("/token/:user/:password_token/:token", h.ResetPasswordSubmit).Name = types.RouteResetPasswordSubmit
}

func (h *Auth) ForgotPasswordPage(ctx echo.Context) error {
	p := page.New(ctx)
	p.Title = "Forgot password"
	p.TemplComponent = auth2.ForgotPassword(form.Get[types.ForgotPasswordForm](ctx))

	return h.RenderPageAuth(ctx, p)
}

func (h *Auth) ForgotPasswordSubmit(ctx echo.Context) error {
	var input types.ForgotPasswordForm

	succeed := func() error {
		form.Clear(ctx)
		msg.Success(ctx, "An email containing a link to reset your password will be sent to this address if it exists in our system.")
		return h.ForgotPasswordPage(ctx)
	}

	failed := func(message string) error {
		msg.Warning(ctx, message)
		return h.ForgotPasswordPage(ctx)
	}

	err := form.Submit(ctx, &input)

	switch err.(type) {
	case nil:
	case validator.ValidationErrors:
		return h.ForgotPasswordPage(ctx)
	default:
		return err
	}

	// Attempt to load the user
	u, err := getUserByEmail(ctx, err, h, input.Email)

	switch err.(type) {
	case nil:
	default:
		if err.Error() == "sql: no rows in result set" {
			return failed("User not found with email " + input.Email)
		}
		return failed(err.Error())
	}

	// Generate the token
	token, pt, err := h.auth.GeneratePasswordResetToken(ctx, u.ID)
	if err != nil {
		return fail(err, "error generating password reset token")
	}

	log.Ctx(ctx).Info("generated password reset token",
		"user_id", u.ID,
	)

	// Email the user
	url := ctx.Echo().Reverse(types.RouteResetPassword, u.ID, pt.ID, token)
	err = h.mail.
		Compose().
		To(u.Email).
		Subject("Reset your password").
		Body(fmt.Sprintf("Go here to reset your password: %s", url)).
		Send(ctx)

	if err != nil {
		return fail(err, "error sending password reset email")
	}

	return succeed()
}

func getUserByEmail(ctx echo.Context, err error, h *Auth, email string) (*types.User, error) {
	u := new(types.User)
	err = h.db.NewSelect().Model(u).Where("email = ?", email).Scan(ctx.Request().Context())
	return u, err
}

func (h *Auth) LoginPage(ctx echo.Context) error {
	p := page.New(ctx)
	p.Title = "ログイン"
	p.TemplComponent = auth2.Login(form.Get[types.LoginForm](ctx))
	return h.RenderPageAuth(ctx, p)
}

func (h *Auth) LoginSubmit(ctx echo.Context) error {
	var input types.LoginForm

	authFailed := func() error {
		input.SetFieldError("Email", "")
		input.SetFieldError("Password", "")
		msg.Warning(ctx, "ユーザー名またはパスワードが無効です。")
		return h.LoginPage(ctx)
	}

	err := form.Submit(ctx, &input)

	switch err.(type) {
	case nil:
	case validator.ValidationErrors:
		return h.LoginPage(ctx)
	default:
		return err
	}

	// Attempt to load the user
	u, err := getUserByEmail(ctx, err, h, input.Email)
	switch err.(type) {
	case nil:
	default:
		if err.Error() == "sql: no rows in result set" {
			return authFailed()
		}
		return fail(err, "error querying user during login")
	}

	// Check if the password is correct
	err = h.auth.CheckPassword(input.Password, u.Password)
	if err != nil {
		return authFailed()
	}

	// Log the user in
	err = h.auth.Login(ctx, u.ID)
	if err != nil {
		return fail(err, "unable to log in user")
	}

	msg.Success(ctx, fmt.Sprintf("<strong>%s</strong>　様、ようこそ！", u.Name))

	if h.IsUnpolyRequest(ctx) {
		return h.SendUnpolyEvents(h.AuthChangeEvent, ctx)
	}

	return redirect.New(ctx).Route(types.RouteHome).Go()
}

func (h *Auth) Logout(ctx echo.Context) error {
	if err := h.auth.Logout(ctx); err == nil {
		msg.Success(ctx, "ログアウトしました。")
	} else {
		msg.Danger(ctx, "エラーが発生しました。")
	}

	if h.IsUnpolyRequest(ctx) {
		return h.SendUnpolyEvents(h.AuthChangeEvent, ctx)
	}

	return redirect.New(ctx).Route(types.RouteLogin).Go()
}

func (h *Auth) RegisterPage(ctx echo.Context) error {
	p := page.New(ctx)
	p.Title = "ユーザー登録"
	p.TemplComponent = auth2.Register(form.Get[types.RegisterForm](ctx))

	return h.RenderPageAuth(ctx, p)
}

func (h *Auth) RegisterSubmit(ctx echo.Context) error {
	var input types.RegisterForm

	err := form.Submit(ctx, &input)

	switch err.(type) {
	case nil:
	case validator.ValidationErrors:
		return h.RegisterPage(ctx)
	default:
		return err
	}

	// Hash the password
	pwHash, err := h.auth.HashPassword(input.Password)
	if err != nil {
		return fail(err, "unable to hash password")
	}

	// Attempt creating the user
	u := &types.User{
		Name:      input.Name,
		Email:     input.Email,
		Password:  pwHash,
		CreatedAt: time.Now().Unix(),
	}
	_, err = h.db.NewInsert().Model(u).Exec(ctx.Request().Context())

	switch err.(type) {
	case nil:
		log.Ctx(ctx).Info("user created",
			"user_name", u.Name,
			"user_id", u.ID,
		)
	default:
		if err.Error() == "UNIQUE constraint failed: users.email" {
			msg.Warning(ctx, "A user with this email address already exists. Please log in.")
			return redirect.New(ctx).Route(types.RouteLogin).Go()
		}
		return fail(err, "unable to create user")
	}

	// Log the user in
	err = h.auth.Login(ctx, u.ID)
	if err != nil {
		log.Ctx(ctx).Error("unable to log user in",
			"error", err,
			"user_id", u.ID,
		)
		msg.Info(ctx, "Your account has been created.")
		return redirect.New(ctx).Route(types.RouteLogin).Go()
	}

	msg.Success(ctx, "Your account has been created. You are now logged in.")

	// Send the verification email
	h.sendVerificationEmail(ctx, u)

	return redirect.New(ctx).Route(types.RouteHome).Go()
}

func (h *Auth) sendVerificationEmail(ctx echo.Context, usr *types.User) {
	// Generate a token
	token, err := h.auth.GenerateEmailVerificationToken(usr.Email)
	if err != nil {
		log.Ctx(ctx).Error("unable to generate email verification token",
			"user_id", usr.ID,
			"error", err,
		)
		return
	}

	// Send the email
	url := ctx.Echo().Reverse(types.RouteVerifyEmail, token)
	err = h.mail.
		Compose().
		To(usr.Email).
		Subject("Confirm your email address").
		Body(fmt.Sprintf("Click here to confirm your email address: %s", url)).
		Send(ctx)

	if err != nil {
		log.Ctx(ctx).Error("unable to send email verification link",
			"user_id", usr.ID,
			"error", err,
		)
		return
	}

	msg.Info(ctx, "An email was sent to you to verify your email address.")
}

func (h *Auth) ResetPasswordPage(ctx echo.Context) error {
	p := page.New(ctx)
	p.Title = "Reset password"
	p.TemplComponent = auth2.ResetPassword(form.Get[types.ResetPasswordForm](ctx))

	return h.RenderPageAuth(ctx, p)
}

func (h *Auth) ResetPasswordSubmit(ctx echo.Context) error {
	var input types.ResetPasswordForm

	err := form.Submit(ctx, &input)

	switch err.(type) {
	case nil:
	case validator.ValidationErrors:
		return h.ResetPasswordPage(ctx)
	default:
		return err
	}

	// Hash the new password
	hash, err := h.auth.HashPassword(input.Password)
	if err != nil {
		return fail(err, "unable to hash password")
	}

	// Get the requesting user
	usr := ctx.Get(context.UserKey).(*types.User)

	// Update the user
	u := types.User{
		Password: hash,
		ID:       usr.ID,
	}
	_, err = h.db.NewUpdate().Model(u).Column("password").WherePK().Exec(ctx.Request().Context())
	//Auth_UpdatePassword(ctx.Request().Context(), )

	if err != nil {
		return fail(err, "unable to update password")
	}

	// Delete all password tokens for this user
	err = h.auth.DeletePasswordTokens(ctx, usr.ID)
	if err != nil {
		return fail(err, "unable to delete password tokens")
	}

	msg.Success(ctx, "Your password has been updated.")
	return redirect.New(ctx).Route(types.RouteLogin).Go()
}

func (h *Auth) VerifyEmail(ctx echo.Context) error {
	var usr types.User

	// Validate the token
	token := ctx.Param("token")
	email, err := h.auth.ValidateEmailVerificationToken(token)
	if err != nil {
		msg.Warning(ctx, "The link is either invalid or has expired.")
		return redirect.New(ctx).Route(types.RouteHome).Go()
	}

	// Check if it matches the authenticated user
	if u := ctx.Get(context.AuthenticatedUserKey); u != nil {
		authUser := u.(types.User)

		if authUser.Email == email {
			usr = authUser
		}
	}
	/*
		// Query to find a matching user, if needed
		if usr == nil {
			usr, err = h.db.GetUserByEmail(ctx.Request().Context(), email)

			if err != nil {
				return fail(err, "query failed loading email verification token user")
			}
		}
	*/
	// Verify the user, if needed
	if !usr.Verified {
		usr.Verified = true
		//usr, err = h.db.Auth_MarkUserAsVerified(ctx.Request().Context(), usr.ID)
		_, err = h.db.NewUpdate().Model(usr).Column("verified").WherePK().Exec(ctx.Request().Context())
		if err != nil {
			return fail(err, "failed to set user as verified")
		}
	}

	msg.Success(ctx, "Your email has been successfully verified.")
	return redirect.New(ctx).Route(types.RouteHome).Go()
}
