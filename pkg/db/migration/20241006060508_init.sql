-- +goose Up
-- +goose StatementBegin

create table backlite_tasks
(
	id               text              not null primary key,
	created_at       integer           not null,
	queue            text              not null,
	task             blob              not null,
	wait_until       integer,
	claimed_at       integer,
	last_executed_at integer,
	attempts         integer default 0 not null
) strict;

create index idx_backlite_tasks_wait_until on backlite_tasks (wait_until) where wait_until IS NOT NULL;

create table backlite_tasks_completed
(
	id                  text    not null primary key,
	created_at          integer not null,
	queue               text    not null,
	last_executed_at    integer,
	attempts            integer not null,
	last_duration_micro integer,
	succeeded           integer,
	task                blob,
	expires_at          integer,
	error               text
) strict;


create table m_pref
(
	id            integer                                   not null primary key autoincrement,
	name          text,
	display_order integer,
	roms_id       integer,

	created_at    integer   default (strftime('%s', 'now')) not null,
	created_by    integer,
	updated_at    integer,
	updated_by    integer,
	deleted_at    timestamp default null
);

create table m_locations
(
	id         integer                                   not null primary key autoincrement,
	lat_lng    text                                      not null,
	zip        text,
	pref_id    integer
		constraint fk_m_locations_pref_id references m_pref (id),
	city_cd    text,
	add1       text,
	add1_kana  text,
	add2       text,
	add2_kana  text,
	add3       text,
	add3_kana  text,
	tel        text,
	fax        text,
	unique_id  text,

	created_at integer   default (strftime('%s', 'now')) not null,
	created_by integer,
	updated_at integer,
	updated_by integer,
	deleted_at timestamp default null
);

create table m_group
(
	id          integer                                   not null primary key autoincrement,
	nm          text                                      not null,
	group_nm    text,
	group_kana  text,
	group_type  smallint,
	item_cat    text,
	location_id integer
		constraint fk_m_group_location_id references m_locations (id),
	opt1        text,
	opt2        text,
	roms_id     integer,


	created_at  integer   default (strftime('%s', 'now')) not null,
	created_by  integer,
	updated_at  integer,
	updated_by  integer,
	deleted_at  timestamp default null
);

create index idx_m_group_group_type on m_group (group_type);
create index idx_m_group_item_cat on m_group (item_cat);
create index idx_m_group_deleted_at on m_group (deleted_at);


create table m_users
(
	id         integer                                   not null primary key autoincrement,
	name       text                                      not null,
	email      text                                      not null,
	password   text                                      not null,
	verified   bool      default 0                       not null,

	created_at integer   default (strftime('%s', 'now')) not null,
	created_by integer,
	updated_at integer,
	updated_by integer,
	deleted_at timestamp default null
);

create table password_tokens
(
	id                  integer  not null primary key autoincrement,
	hash                text     not null,
	created_at          datetime not null,
	password_token_user integer  not null
		constraint password_tokens_users_user references m_users
);

create unique index u_idx_m_users_email on m_users (email);


create table t_allocation_runs
(
	id           integer                                   not null primary key autoincrement,
	fulfill_date date                                      not null,
	status       smallint,

	created_at   integer   default (strftime('%s', 'now')) not null,
	created_by   integer,
	updated_at   integer,
	updated_by   integer,
	deleted_at   timestamp default null
);

create table t_solutions
(
	id          integer                                   not null primary key autoincrement,
	run_id      integer                                   not null
		constraint fk_t_solutions_run_id references t_allocation_runs (id),
	total_time  integer,
	visits      text,
	score       text,
	is_feasible int,

	created_at  integer   default (strftime('%s', 'now')) not null,
	created_by  integer,
	updated_at  integer,
	updated_by  integer,
	deleted_at  timestamp default null
);


create table m_items
(
	id         integer                                   not null primary key autoincrement,
	name       text                                      not null,
	snm1       text,
	snm2       text,
	category   integer,
	rolly1_flg boolean   default false,
	rolly2_flg boolean   default false,
	rolly3_flg boolean   default false,
	bcolor     text,
	fcolor     text,
	sort       integer,
	opt2       text,
	opt3       text,
	poison_flg boolean   default false,
	roms_id    integer,

	created_at integer   default (strftime('%s', 'now')) not null,
	created_by integer,
	updated_at integer,
	updated_by integer,
	deleted_at timestamp default null
);

create index idx_m_items_category on m_items (category);
create index idx_m_items_rolly_flags on m_items (rolly1_flg, rolly2_flg, rolly3_flg);
create index idx_m_items_deleted_at on m_items (deleted_at);
-- create unique index u_idx_m_items_name ON m_items (name); --  WHERE deleted_at IS NULL;


create table m_shipper
(
	id          integer                                   not null primary key autoincrement,
	charge_cd   text                                      not null,
	nm          text                                      not null,
	kana        text,
	snm1        text,
	snm2        text,
	location_id integer                                   not null
		constraint fk_m_shipper_location_id references m_locations (id),

	opt1        text,
	opt2        text,
	opt3        text,
	opt4        text,
	opt5        text,
	roms_id     integer,

	created_at  integer   default (strftime('%s', 'now')) not null,
	created_by  integer,
	updated_at  integer,
	updated_by  integer,
	deleted_at  timestamp default null
);

create index idx_m_shipper_deleted_at on m_shipper (deleted_at);


create table m_pickups
(
	id          integer                                   not null primary key autoincrement,
	nm          text,
	kana        text,
	snm1        text,
	snm2        text,
	location_id integer                                   not null
		constraint fk_m_pickups_location_id references m_locations (id),

	start_time  text,
	lunch_time  text,
	end_time    text,
	opt1        text,
	opt2        text,
	opt3        text,
	roms_id     integer,

	created_at  integer   default (strftime('%s', 'now')) not null,
	created_by  integer,
	updated_at  integer,
	updated_by  integer,
	deleted_at  timestamp default null
);

create index idx_m_pickup_deleted_at on m_pickups (deleted_at);


create table m_pickup_group
(
	pickup_id integer not null references m_pickups on delete cascade,
	group_id  integer not null references m_group,
	primary key (pickup_id, group_id)
);


create table m_dest_company
(
	id         integer                                   not null primary key autoincrement,
	name       text                                      not null,
	kana       text,
	opt1       text,
	opt2       text,
	opt3       text,
	roms_id    integer,

	created_at integer   default (strftime('%s', 'now')) not null,
	created_by integer,
	updated_at integer,
	updated_by integer,
	deleted_at timestamp default null
);

create index idx_m_dest_company_deleted_at on m_dest_company (deleted_at);



create table m_dest_area
(
	id              integer                                   not null primary key autoincrement,
	dest_company_id integer                                   not null references m_dest_company,
	serial_no       integer   default 1,
	nm              text,
	kana            text,

	opt1            text,
	opt2            text,
	opt3            text,
	location_id     integer                                   not null
		constraint fk_m_dest_area_location_id references m_locations (id),
	roms_id         integer,

	created_at      integer   default (strftime('%s', 'now')) not null,
	created_by      integer,
	updated_at      integer,
	updated_by      integer,
	deleted_at      timestamp default null
);

create index idx_m_dest_area_deleted_at on m_dest_area (deleted_at);
create unique index u_idx_m_dest_area_company_id_serial_no on m_dest_area (dest_company_id, serial_no);



create table m_dest_spot
(
	id           integer                                   not null primary key autoincrement,
	dest_area_id integer                                   not null references m_dest_area,
	nm           text,
	spot         text,
	item_id      integer
		constraint fk_m_dest_spot_item_id references m_items (id),
	kana1        text,
	kana2        text,
	snm1         text,
	snm2         text,
	dept1        text,
	dept2        text,
	person1      text,
	person2      text,
	contact1     text,
	contact2     text,
	cmt1_flg     boolean   default false,
	cmt1         text,
	cmt2_flg     boolean   default false,
	cmt2         text,
	cmt3_flg     boolean   default false,
	cmt3         text,
	cmt4_flg     boolean   default false,
	cmt4         text,
	cmt5_flg     boolean   default false,
	cmt5         text,
	cmt6_flg     boolean   default false,
	cmt6         text,
	cmt7_flg     boolean   default false,
	cmt7         text,
	cmt8_flg     boolean   default false,
	cmt8         text,
	cmt9_flg     boolean   default false,
	cmt9         text,
	cmt10_flg    boolean   default false,
	cmt10        text,
	file_id      text,
	file1        text,
	file2        text,
	remarks      text,
	roms_id      integer,

	created_at   integer   default (strftime('%s', 'now')) not null,
	created_by   integer,
	updated_at   integer,
	updated_by   integer,
	deleted_at   timestamp default null
);


create index idx_m_dest_spot_deleted_at on m_dest_spot (deleted_at);



create table m_dest_group
(
	id           integer                                   not null primary key autoincrement,
	dest_spot_id integer                                   not null references m_dest_spot,
	group_id     integer                                   not null references m_group,
	dest_cd      text                                      not null,
	shipper_id   integer                                   not null references m_shipper,
	keisu        text,
	fare_type    text,
	fare_dist    text,
	fare_price   text,
	remarks      text,
	roms_id      integer,

	created_at   integer   default (strftime('%s', 'now')) not null,
	created_by   integer,
	updated_at   integer,
	updated_by   integer,
	deleted_at   timestamp default null
);


create index idx_m_dest_group_deleted_at on m_dest_group (deleted_at);



create table m_distances
(
	from_loc_id integer                                   not null,
	to_loc_id   integer                                   not null,
	duration    integer                                   not null,

	created_at  integer   default (strftime('%s', 'now')) not null,
	created_by  integer,
	updated_at  integer,
	updated_by  integer,
	deleted_at  timestamp default null,

	primary key (from_loc_id, to_loc_id),
	foreign key (from_loc_id) references m_locations (id),
	foreign key (to_loc_id) references m_locations (id)
);

/*create table t_orders
(
    id             integer                                   not null primary key autoincrement,
    pickup_id      integer                                   not null,
    destination_id integer                                   not null,
    item_id        integer                                   not null,
    fulfill_date   date                                      not null,

    created_at     integer   default (strftime('%s', 'now')) not null,
    created_by     integer,
    updated_at     integer,
    updated_by     integer,
    deleted_at     timestamp default null,

    foreign key (pickup_id) references m_pickups (id),
    foreign key (destination_id) references m_dest_group (id),
    foreign key (item_id) references m_items (id)
);*/

create table t_orders
(
	id           integer                                   not null primary key autoincrement,
	roms_id      text                                      not null,

	type_no      integer,
	import_key   text,
	dest_cd      text                                      not null,
	item_id      integer                                   not null,
	pickup_id    integer                                   not null
		constraint t_order_fkey3
			references m_pickups,
	volume1      text                                      not null,
	unit1        text                                      not null,
	volume2      text,
	unit2        integer,
	requirement  text,
	ship_date    date                                      not null,
	deli_date    date                                      not null,
	set_time     text,
	cmt1         text,
	cmt2         text,
	remarks      text,
	order_status smallint  default 1,
	p_no         integer,
	opt1         text,
	opt2         text,
	opt3         text,

	lock_u_id    text,


	created_at   integer   default (strftime('%s', 'now')) not null,
	created_by   integer,
	updated_at   integer,
	updated_by   integer,
	deleted_at   timestamp default null,


	foreign key (pickup_id) references m_pickups (id),
	foreign key (dest_cd) references m_dest_group (dest_cd),
	foreign key (item_id) references m_items (id)
);


create index t_order_idx1
	on t_orders (dest_cd);

create index t_order_idx2
	on t_orders (item_id);

create index t_order_idx3
	on t_orders (pickup_id);

create index t_order_idx4
	on t_orders (deli_date);

create index t_order_idx5
	on t_orders (p_no);

create index t_order_idx6
	on t_orders (deleted_at);

create index t_order_idx7
	on t_orders (type_no);



create table m_drivers
(
	id         integer                                   not null primary key autoincrement,
	name       text unique,

	created_at integer   default (strftime('%s', 'now')) not null,
	created_by integer,
	updated_at integer,
	updated_by integer,
	deleted_at timestamp default null
);

create table m_vehicles
(
	id              integer                                   not null primary key autoincrement,
	name            text unique,
	color           text,
	capacity        numeric(10, 2)                            not null,
	capacity_liters numeric(10, 2)                            not null,

	created_at      integer   default (strftime('%s', 'now')) not null,
	created_by      integer,
	updated_at      integer,
	updated_by      integer,
	deleted_at      timestamp default null
);

create table driver_material_categories
(
	driver_id integer not null,
	item_id   integer not null,
	primary key (driver_id, item_id),
	foreign key (driver_id) references m_drivers (id),
	foreign key (item_id) references m_items (id)
);

create table vehicle_material_categories
(
	vehicle_id integer not null,
	item_id    integer not null,
	primary key (vehicle_id, item_id),
	foreign key (vehicle_id) references m_vehicles (id),
	foreign key (item_id) references m_items (id)
);

create table process_details
(
	id          integer not null primary key autoincrement,
	type        integer not null,
	description text,
	start       timestamp        default current_timestamp not null,
	end         timestamp,
	status      integer not null default 0
);

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

drop table backlite_tasks;
drop table backlite_tasks_completed;
drop table password_tokens;
drop table m_users;
drop table m_pref;
drop table m_locations;
drop table m_group;
drop table t_allocation_runs;
drop table t_solutions;
drop table m_items;
drop table m_shipper;
drop table m_pickups;
drop table m_pickup_group;
drop table m_dest_company;
drop table m_dest_area;
drop table m_dest_spot;
drop table m_dest_group;
drop table m_distances;
drop table t_orders;
drop table m_drivers;
drop table driver_material_categories;
drop table m_vehicles;
drop table vehicle_material_categories;
drop table process_details;


-- +goose StatementEnd
