package services

import (
	"context"
	"loms/pkg/services/repo"
	"loms/pkg/types"
)

type MaterialCategoryCrudService struct {
	store repo.MaterialCategoryStore
}

func NewMaterialCategoryCrudService(store repo.MaterialCategoryStore) *MaterialCategoryCrudService {
	return &MaterialCategoryCrudService{store: store}
}

func (crud *MaterialCategoryCrudService) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.ItemEntity], error) {
	return crud.store.FindAllPageable(ctx, pageRequest)
}

func (crud *MaterialCategoryCrudService) FindAll(ctx context.Context) ([]types.ItemEntity, error) {
	return crud.store.FindAll(ctx)
}

func (crud *MaterialCategoryCrudService) FindByID(ctx context.Context, id int64) (*types.ItemEntity, error) {
	return crud.store.FindByID(ctx, id)
}

func (crud *MaterialCategoryCrudService) Upsert(ctx context.Context, entity *types.ItemEntity) error {
	if entity.ID != 0 {
		return crud.store.Update(ctx, entity)
	}
	return crud.store.Save(ctx, entity)
}

func (crud *MaterialCategoryCrudService) Delete(ctx context.Context, id int64) error {
	return crud.store.Delete(ctx, id)
}
