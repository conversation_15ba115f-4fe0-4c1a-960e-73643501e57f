package services

import (
	"context"
	"loms/pkg/services/repo"
	"loms/pkg/types"
)

type JobCrudService struct {
	store               repo.JobStore
	processDetailsStore repo.ProcessDetailsStore
	categoryStore       repo.MaterialCategoryStore
	locationStore       repo.LocationStore
}

func NewJobCrudService(store repo.JobStore,
	processDetailsStore repo.ProcessDetailsStore,
	categoryStore repo.MaterialCategoryStore,
	locationStore repo.LocationStore,
) *JobCrudService {
	return &JobCrudService{
		store:               store,
		processDetailsStore: processDetailsStore,
		categoryStore:       categoryStore,
		locationStore:       locationStore,
	}
}

func (crud *JobCrudService) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.JobEntity], error) {
	return crud.store.FindAllPageable(ctx, pageRequest)
}

func (crud *JobCrudService) GetJobsByFulfillDate(ctx context.Context, date int64) ([]types.JobDto, error) {
	return crud.store.FindByFulfillDate(ctx, date)
}

func (crud *JobCrudService) FindByID(ctx context.Context, id int64) (*types.JobEntity, error) {
	return crud.store.FindByID(ctx, id)
}

func (crud *JobCrudService) BulkUpload(ctx context.Context, jobs []types.JobEntity) ([]types.JobEntity, error) {
	return crud.store.BulkUpload(ctx, jobs)
}

func (crud *JobCrudService) Upsert(ctx context.Context, entity *types.JobEntity) error {
	if entity.ID != 0 {
		return crud.store.Update(ctx, entity)
	}
	return crud.store.Save(ctx, entity)
}

func (crud *JobCrudService) FindAllCategories(ctx context.Context) ([]types.ItemEntity, error) {
	return crud.categoryStore.FindAll(ctx)
}

/*func (crud *JobCrudService) FindAllPickupsDeliveries(ctx context.Context) ([]types.LocationEntity, []types.LocationEntity, error) {
	allLocations, err := crud.locationStore.FindAll(ctx)
	if err != nil {
		return nil, nil, err
	}

	pickups := lo.Filter(allLocations, func(location types.LocationEntity, _ int) bool {
		return location.LocationType == types.PICKUP && !location.IsDepot
	})

	deliveries := lo.Filter(allLocations, func(location types.LocationEntity, _ int) bool {
		return location.LocationType == types.DELIVERY
	})

	return pickups, deliveries, nil
}*/
