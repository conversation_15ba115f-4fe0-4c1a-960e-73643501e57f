package repo

import (
	"context"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"loms/pkg/types"
	"strconv"

	"github.com/uptrace/bun"
)

type (
	DestinationAreaStore interface {
		FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.DestinationAreaEntity], error)
		FindAllByIds(ctx context.Context, ids []int64) ([]types.DestinationAreaEntity, error)
		FindAllWithFilters(ctx context.Context, coordinates []string) ([]types.DestinationAreaEntity, error)
		FindByID(ctx context.Context, id int64) (*types.DestinationAreaEntity, error)
		Save(ctx context.Context, location *types.DestinationAreaEntity) error
		Update(ctx context.Context, location *types.DestinationAreaEntity) error
		BulkUpload(ctx context.Context, locations []types.DestinationAreaEntity) ([]types.DestinationAreaEntity, error)
	}
	DestinationAreaRepo struct {
		db *bun.DB
	}
)

func NewDestinationAreaRepo(db *bun.DB) *DestinationAreaRepo {
	return &DestinationAreaRepo{db: db}
}

func (repo *DestinationAreaRepo) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.DestinationAreaEntity], error) {
	var entities []types.DestinationAreaEntity
	if pageRequest.Sort == "Id" {
		pageRequest.Sort = "da.ID"
	}
	count, err := repo.db.NewSelect().Model(&entities).
		Relation("Location").
		OrderExpr(fmt.Sprintf("%s %s", pageRequest.Sort, pageRequest.Order)).
		Limit(pageRequest.Size).
		Offset((pageRequest.Page - 1) * pageRequest.Size).ScanAndCount(ctx)

	if err != nil {
		return nil, err
	}

	return &types.Page[types.DestinationAreaEntity]{
		Content: entities,
		Total:   count,
		Paging:  pageRequest,
	}, nil
}

func (repo *DestinationAreaRepo) FindAllByIds(ctx context.Context, ids []int64) ([]types.DestinationAreaEntity, error) {
	var areaEntities []types.DestinationAreaEntity
	err := repo.db.NewSelect().Model(&areaEntities).
		Relation("Location").
		Where("da.id IN (?)", bun.In(ids)).
		Scan(ctx)
	return areaEntities, err
}

func (repo *DestinationAreaRepo) FindByID(ctx context.Context, id int64) (*types.DestinationAreaEntity, error) {
	locations, err := repo.FindAllByIds(ctx, []int64{id})

	if err != nil {
		return nil, err
	}

	if len(locations) == 0 {
		return nil, errors.New("Location with id " + strconv.FormatInt(id, 10) + " not found")
	}

	return &locations[0], nil
}

func (repo *DestinationAreaRepo) FindAllWithFilters(ctx context.Context, coordinates []string) ([]types.DestinationAreaEntity, error) {
	var locations []types.DestinationAreaEntity
	query := repo.db.NewSelect().Model(&locations).Relation("Location")

	if len(coordinates) > 0 {
		query.WhereGroup(" AND ", func(q *bun.SelectQuery) *bun.SelectQuery {
			for _, coord := range coordinates {
				q = q.WhereOr("location.lat_lon = ?", coord)
			}
			return q
		})
	}

	err := query.Scan(ctx)
	return locations, err
}

func (repo *DestinationAreaRepo) Save(ctx context.Context, location *types.DestinationAreaEntity) error {
	_, err := repo.db.NewInsert().Model(location).Exec(ctx)
	return err
}
func (repo *DestinationAreaRepo) Update(ctx context.Context, location *types.DestinationAreaEntity) error {
	_, err := repo.db.NewUpdate().Model(location).WherePK().Exec(ctx)
	return err
}

func (repo *DestinationAreaRepo) BulkUpload(ctx context.Context, locations []types.DestinationAreaEntity) ([]types.DestinationAreaEntity, error) {
	locationEntities := lo.Map(locations, func(location types.DestinationAreaEntity, _ int) types.LocationEntity {
		return *location.Location
	})

	err := repo.db.NewUpdate().
		Model(&locations).
		Column("nm").
		Bulk().
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	err = repo.db.NewUpdate().
		Model(&locationEntities).
		Column("lat_lng", "add1", "add1_kana", "add2", "add2_kana", "add3", "add3_kana").
		Bulk().
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	return locations, nil
}
