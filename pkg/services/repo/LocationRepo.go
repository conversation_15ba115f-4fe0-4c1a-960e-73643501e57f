package repo

import (
	"context"
	"errors"
	"fmt"
	"loms/pkg/types"
	"strconv"

	"github.com/uptrace/bun"
)

type (
	LocationStore interface {
		FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.LocationEntity], error)
		FindAll(ctx context.Context) ([]types.LocationEntity, error)
		FindAllWhoseLatLngIsNotEmpty(ctx context.Context) ([]types.LocationEntity, error)
		FindAllByIds(ctx context.Context, ids []int64) ([]types.LocationEntity, error)
		FindAllDepots(ctx context.Context) ([]types.ProblemLocation, error)
		FindAllWithFilters(ctx context.Context, coordinates []string) ([]types.LocationEntity, error)
		FindByID(ctx context.Context, id int64) (*types.LocationEntity, error)
		Save(ctx context.Context, location *types.LocationEntity) error
		Update(ctx context.Context, location *types.LocationEntity) error
		BulkUpload(ctx context.Context, locations []types.LocationEntity) ([]types.LocationEntity, error)
	}
	LocationRepo struct {
		db *bun.DB
	}
)

func NewLocationRepo(db *bun.DB) *LocationRepo {
	return &LocationRepo{db: db}
}

func (repo *LocationRepo) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.LocationEntity], error) {
	var locations []types.LocationEntity
	count, err := repo.db.NewSelect().Model(&locations).
		OrderExpr(fmt.Sprintf("%s %s", pageRequest.Sort, pageRequest.Order)).
		Limit(pageRequest.Size).
		Offset((pageRequest.Page - 1) * pageRequest.Size).ScanAndCount(ctx)

	if err != nil {
		return nil, err
	}

	return &types.Page[types.LocationEntity]{
		Content: locations,
		Total:   count,
		Paging:  pageRequest,
	}, nil
}

func (repo *LocationRepo) FindAll(ctx context.Context) ([]types.LocationEntity, error) {
	var locations []types.LocationEntity
	err := repo.db.NewSelect().Model(&locations).Scan(ctx)
	return locations, err
}

func (repo *LocationRepo) FindAllWhoseLatLngIsNotEmpty(ctx context.Context) ([]types.LocationEntity, error) {
	var locations []types.LocationEntity
	err := repo.db.NewSelect().Model(&locations).
		Where("lat_lng IS NOT NULL AND lat_lng <> '-'").
		Scan(ctx)
	return locations, err
}

func (repo *LocationRepo) FindAllByIds(ctx context.Context, ids []int64) ([]types.LocationEntity, error) {
	var locations []types.LocationEntity
	err := repo.db.NewSelect().Model(&locations).
		Where("id IN (?)", bun.In(ids)).
		Scan(ctx)
	return locations, err
}

func (repo *LocationRepo) FindAllDepots(ctx context.Context) ([]types.ProblemLocation, error) {
	var honshaId int64 = 1
	var locations []types.ProblemLocation
	err := repo.db.NewRaw(
		`
			select mg.id,
				   group_nm description,
				   unique_id,
				   0                                                                             as location_type,
				   1                                                                             as is_depot,
				   substr(lat_lng, 1, instr(lat_lng, ', ') - 1)                                  as lat,
				   substr(lat_lng, instr(lat_lng, ', ') + 2)                                     as lon,
				   concat_ws('、', ifnull(ml.add1, ''), ifnull(ml.add2, ''), ifnull(ml.add3, '')) as address,
				   32400000 as ready_time,
				   70800000 as due_time,
				   1200000 as service_duration
			from m_group mg
					 inner join main.m_locations ml on ml.id = mg.location_id
			where mg.id = ?;
			`, honshaId).
		Scan(ctx, &locations)

	return locations, err
}

func (repo *LocationRepo) FindAllWithFilters(ctx context.Context, coordinates []string) ([]types.LocationEntity, error) {
	var locations []types.LocationEntity
	query := repo.db.NewSelect().Model(&locations)

	if len(coordinates) > 0 {
		query.WhereGroup(" AND ", func(q *bun.SelectQuery) *bun.SelectQuery {
			for _, coord := range coordinates {
				q = q.WhereOr("lat_lon = ?", coord)
			}
			return q
		})
	}

	err := query.Scan(ctx)
	return locations, err
}

func (repo *LocationRepo) FindByID(ctx context.Context, id int64) (*types.LocationEntity, error) {
	locations, err := repo.FindAllByIds(ctx, []int64{id})

	if err != nil {
		return nil, err
	}

	if len(locations) == 0 {
		return nil, errors.New("Location with id " + strconv.FormatInt(id, 10) + " not found")
	}

	return &locations[0], nil
}

func (repo *LocationRepo) Save(ctx context.Context, location *types.LocationEntity) error {
	_, err := repo.db.NewInsert().Model(location).Exec(ctx)
	return err
}
func (repo *LocationRepo) Update(ctx context.Context, location *types.LocationEntity) error {
	_, err := repo.db.NewUpdate().Model(location).WherePK().Exec(ctx)
	return err
}

func (repo *LocationRepo) BulkUpload(ctx context.Context, locations []types.LocationEntity) ([]types.LocationEntity, error) {
	err := repo.db.NewInsert().Model(&locations).Returning("*").Scan(ctx)
	if err != nil {
		return nil, err
	}
	return locations, nil
}
