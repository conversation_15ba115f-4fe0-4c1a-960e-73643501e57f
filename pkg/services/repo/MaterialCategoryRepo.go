package repo

import (
	"context"
	"fmt"
	"loms/pkg/types"
	"time"

	"github.com/uptrace/bun"
)

type (
	MaterialCategoryStore interface {
		FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.ItemEntity], error)
		FindAll(ctx context.Context) ([]types.ItemEntity, error)
		FindByID(ctx context.Context, id int64) (*types.ItemEntity, error)
		Save(ctx context.Context, materialCategory *types.ItemEntity) error
		Update(ctx context.Context, materialCategory *types.ItemEntity) error
		Delete(ctx context.Context, id int64) error
	}
	MaterialCategoryRepo struct {
		db *bun.DB
	}
)

func NewMaterialCategoryRepo(db *bun.DB) *MaterialCategoryRepo {
	return &MaterialCategoryRepo{db: db}
}

func (repo *MaterialCategoryRepo) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.ItemEntity], error) {
	var entities []types.ItemEntity
	count, err := repo.db.NewSelect().Model(&entities).
		OrderExpr(fmt.Sprintf("%s %s", pageRequest.Sort, pageRequest.Order)).
		Limit(pageRequest.Size).
		Offset((pageRequest.Page - 1) * pageRequest.Size).ScanAndCount(ctx)

	if err != nil {
		return nil, err
	}

	return &types.Page[types.ItemEntity]{
		Content: entities,
		Total:   count,
		Paging:  pageRequest,
	}, nil
}

func (repo *MaterialCategoryRepo) FindAll(ctx context.Context) ([]types.ItemEntity, error) {
	var entities []types.ItemEntity
	err := repo.db.NewSelect().Model(&entities).Scan(ctx)
	return entities, err
}

func (repo *MaterialCategoryRepo) FindByID(ctx context.Context, id int64) (*types.ItemEntity, error) {
	entity := &types.ItemEntity{ID: id}
	err := repo.db.NewSelect().Model(entity).WherePK().Scan(ctx)
	return entity, err
}

func (repo *MaterialCategoryRepo) Save(ctx context.Context, materialCategory *types.ItemEntity) error {
	materialCategory.CreatedAt = time.Now().Unix()
	_, err := repo.db.NewInsert().Model(materialCategory).Exec(ctx)
	return err
}

func (repo *MaterialCategoryRepo) Update(ctx context.Context, materialCategory *types.ItemEntity) error {
	materialCategory.UpdatedAt = time.Now().Unix()
	_, err := repo.db.NewUpdate().Model(materialCategory).WherePK().Exec(ctx)
	return err
}

// Delete is actually soft delete with the help of Bun ORM
func (repo *MaterialCategoryRepo) Delete(ctx context.Context, id int64) error {
	_, err := repo.db.NewDelete().Model(&types.ItemEntity{ID: id}).WherePK().Exec(ctx)
	return err
}
