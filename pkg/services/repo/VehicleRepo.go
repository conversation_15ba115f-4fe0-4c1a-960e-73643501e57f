package repo

import (
	"context"
	"fmt"
	"loms/pkg/types"

	"github.com/uptrace/bun"
)

type (
	VehicleStore interface {
		FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.VehicleEntity], error)
		FindAll(ctx context.Context) ([]types.VehicleEntity, error)
		FindByID(ctx context.Context, id int64) (*types.VehicleEntity, error)
		FindByIDs(ctx context.Context, ids []int64) ([]types.VehicleEntity, error)
		FindByNotInIDs(ctx context.Context, ids []int64) ([]types.VehicleEntity, error)
		Save(ctx context.Context, vehicle *types.VehicleEntity) error
		Update(ctx context.Context, vehicle *types.VehicleEntity) error
		DeleteAllVehicleMaterialCategoryByVehicle(ctx context.Context, vehicleId int64) error
		SaveAllVehicleMaterialCategory(ctx context.Context, vehicleId int64, materialCategories []int64) error
	}
	VehicleRepo struct {
		db *bun.DB
	}
)

func NewVehicleRepo(db *bun.DB) *VehicleRepo {
	return &VehicleRepo{db: db}
}

func (repo *VehicleRepo) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.VehicleEntity], error) {
	var entities []types.VehicleEntity
	count, err := repo.db.NewSelect().Model(&entities).
		Relation("MaterialCategories").
		OrderExpr(fmt.Sprintf("%s %s", pageRequest.Sort, pageRequest.Order)).
		Limit(pageRequest.Size).
		Offset((pageRequest.Page - 1) * pageRequest.Size).ScanAndCount(ctx)

	if err != nil {
		return nil, err
	}

	return &types.Page[types.VehicleEntity]{
		Content: entities,
		Total:   count,
		Paging:  pageRequest,
	}, nil
}

func (repo *VehicleRepo) FindAll(ctx context.Context) ([]types.VehicleEntity, error) {
	var jobs []types.VehicleEntity
	err := repo.db.NewSelect().Model(&jobs).Where("group_id = ?", 1).Relation("MaterialCategories").Scan(ctx)
	return jobs, err
}

func (repo *VehicleRepo) FindByID(ctx context.Context, id int64) (*types.VehicleEntity, error) {
	entity := &types.VehicleEntity{ID: id}
	err := repo.db.NewSelect().Model(entity).Relation("MaterialCategories").WherePK().Scan(ctx)
	return entity, err
}

func (repo *VehicleRepo) FindByIDs(ctx context.Context, ids []int64) ([]types.VehicleEntity, error) {
	var entities []types.VehicleEntity
	err := repo.db.NewSelect().Model(&entities).Relation("MaterialCategories").Where("id IN (?) and group_id = 1", bun.In(ids)).Scan(ctx)
	return entities, err
}

func (repo *VehicleRepo) FindByNotInIDs(ctx context.Context, ids []int64) ([]types.VehicleEntity, error) {
	var entities []types.VehicleEntity
	err := repo.db.NewSelect().Model(&entities).Where("id NOT IN (?)", bun.In(ids)).Scan(ctx)
	return entities, err
}

func (repo *VehicleRepo) Save(ctx context.Context, vehicle *types.VehicleEntity) error {
	_, err := repo.db.NewInsert().Model(vehicle).Exec(ctx)
	return err
}

func (repo *VehicleRepo) Update(ctx context.Context, vehicle *types.VehicleEntity) error {
	_, err := repo.db.NewUpdate().Model(vehicle).WherePK().Exec(ctx)
	return err
}

func (repo *VehicleRepo) DeleteAllVehicleMaterialCategoryByVehicle(ctx context.Context, vehicleId int64) error {
	_, err := repo.db.NewDelete().
		Model((*types.VehicleMaterialCategoryEntity)(nil)).
		Where("vehicle_id = ?", vehicleId).Exec(ctx)
	return err
}

func (repo *VehicleRepo) SaveAllVehicleMaterialCategory(ctx context.Context, vehicleId int64, materialCategories []int64) error {
	var entities []types.VehicleMaterialCategoryEntity
	for _, catId := range materialCategories {
		entities = append(entities, types.VehicleMaterialCategoryEntity{
			VehicleID: vehicleId,
			ItemID:    catId,
		})
	}

	if len(entities) > 0 {
		_, err := repo.db.NewInsert().Model(&entities).Exec(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}
