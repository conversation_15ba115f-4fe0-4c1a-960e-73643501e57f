package repo

import (
	"context"
	"github.com/uptrace/bun"
	"loms/pkg/types"
)

type (
	AllocationRunStore interface {
		FindByID(ctx context.Context, id int64) (*types.AllocationRunEntity, error)
		FindByDate(ctx context.Context, date int64) ([]types.AllocationRunEntity, error)
		CountByInProgress(ctx context.Context) (int, error)
		Save(ctx context.Context, allocationRun *types.AllocationRunEntity) (*types.AllocationRunEntity, error)
		Update(ctx context.Context, allocationRun *types.AllocationRunEntity) (*types.AllocationRunEntity, error)
	}

	AllocationRunRepo struct {
		db *bun.DB
	}
)

func NewAllocationRunRepo(db *bun.DB) *AllocationRunRepo {
	return &AllocationRunRepo{db: db}
}

func (repo *AllocationRunRepo) FindByID(ctx context.Context, id int64) (*types.AllocationRunEntity, error) {
	entity := &types.AllocationRunEntity{ID: id}
	err := repo.db.NewSelect().Model(entity).WherePK().Scan(ctx)
	return entity, err
}

func (repo *AllocationRunRepo) FindByDate(ctx context.Context, date int64) ([]types.AllocationRunEntity, error) {

	var runs []types.AllocationRunEntity
	err := repo.db.NewSelect().Model(&runs).
		Where("fulfill_date = ?", date).
		Order("id desc").
		Scan(ctx)

	if err != nil {
		return nil, err
	}
	if runs == nil {
		return []types.AllocationRunEntity{}, nil
	}
	return runs, nil
}

func (repo *AllocationRunRepo) CountByInProgress(ctx context.Context) (int, error) {
	return repo.db.NewSelect().Model((*types.AllocationRunEntity)(nil)).
		Where("status = 1").
		Count(ctx)
}

func (repo *AllocationRunRepo) Save(ctx context.Context, allocationRun *types.AllocationRunEntity) (*types.AllocationRunEntity, error) {
	_, err := repo.db.NewInsert().Model(allocationRun).Returning("id").Exec(ctx)
	if err != nil {
		return nil, err
	}
	return allocationRun, err
}

func (repo *AllocationRunRepo) Update(ctx context.Context, allocationRun *types.AllocationRunEntity) (*types.AllocationRunEntity, error) {
	_, err := repo.db.NewUpdate().Model(allocationRun).Where("id = ?", allocationRun.ID).Exec(ctx)
	if err != nil {
		return nil, err
	}
	return allocationRun, nil
}
