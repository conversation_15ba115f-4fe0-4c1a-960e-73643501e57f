package repo

import (
	"context"
	"errors"
	"fmt"
	"loms/pkg/types"

	"github.com/uptrace/bun"
)

type (
	DistanceStore interface {
		FindAll(ctx context.Context) ([]types.DistanceEntity, error)
		FindAllByLocationIds(ctx context.Context, ids []int64) ([]types.DistanceEntity, error)
		SaveBatch(ctx context.Context, entities []types.DistanceEntity) error
	}

	DistanceRepo struct {
		db *bun.DB
	}
)

// NewDistanceRepo initializes a new DistanceRepo with a given database instance.
func NewDistanceRepo(db *bun.DB) *DistanceRepo {
	return &DistanceRepo{db: db}
}

func (repo *DistanceRepo) FindAll(ctx context.Context) ([]types.DistanceEntity, error) {
	var distances []types.DistanceEntity
	err := repo.db.NewSelect().Model(&distances).Scan(ctx)
	return distances, err
}

// FindAllByLocationIds retrieves all distances where either from_loc_id or to_loc_id matches any of the provided IDs.
func (repo *DistanceRepo) FindAllByLocationIds(ctx context.Context, ids []int64) ([]types.DistanceEntity, error) {
	if len(ids) == 0 {
		return nil, errors.New("location IDs list is empty")
	}

	// Define a temporary table-like structure to hold the location IDs
	locIDs := make([]struct {
		LocID int64 `bun:"loc_id"`
	}, len(ids))
	for i, id := range ids {
		locIDs[i].LocID = id
	}

	var distances []types.DistanceEntity

	// Construct the SQL query with a CTE
	err := repo.db.NewSelect().
		With("input", repo.db.NewValues(&locIDs)).
		Model(&distances).
		Join("JOIN input AS l1 ON dis.from_loc_id = l1.loc_id").
		Join("JOIN input AS l2 ON dis.to_loc_id = l2.loc_id").
		Where("dis.from_loc_id != dis.to_loc_id").
		Column("dis.from_loc_id", "dis.to_loc_id", "dis.duration").
		Scan(ctx)
	if err != nil {
		return nil, fmt.Errorf("error fetching distances: %w", err)
	}

	return distances, nil
}

func (repo *DistanceRepo) SaveBatch(ctx context.Context, entities []types.DistanceEntity) error {
	_, err := repo.db.NewInsert().Model(&entities).
		On("CONFLICT (from_loc_id, to_loc_id) DO UPDATE").
		Set("duration = EXCLUDED.duration").
		Exec(ctx)
	return err
}
