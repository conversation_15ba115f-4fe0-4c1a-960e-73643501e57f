package repo

import (
	"context"
	"errors"
	"fmt"
	"github.com/uptrace/bun"
	"loms/pkg/types"
	"strconv"
)

type (
	JobStore interface {
		FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.JobEntity], error)
		FindAll(ctx context.Context) ([]types.JobEntity, error)
		FindByFulfillDate(ctx context.Context, date int64) ([]types.JobDto, error)
		FindAllByIdsForValidation(ctx context.Context, ids []int64) ([]types.JobDto, error)
		FindAllByIds(ctx context.Context, ids []int64) ([]types.JobEntity, error)
		FindAllWithFilters(ctx context.Context, coordinates []types.Coordinates, names []string) ([]types.JobEntity, error)
		FindByID(ctx context.Context, id int64) (*types.JobEntity, error)
		Save(ctx context.Context, job *types.JobEntity) error
		Update(ctx context.Context, job *types.JobEntity) error
		BulkUpload(ctx context.Context, jobs []types.JobEntity) ([]types.JobEntity, error)
	}
	JobRepo struct {
		db *bun.DB
	}
)

func NewJobRepo(db *bun.DB) *JobRepo {
	return &JobRepo{db: db}
}

func (repo *JobRepo) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.JobEntity], error) {
	var jobs []types.JobEntity
	count, err := repo.db.NewSelect().Model(&jobs).
		Relation("Pickup").
		Relation("Pickup.Location").
		Relation("DestinationGroup").
		Relation("Item").
		OrderExpr(fmt.Sprintf("%s %s", "o."+pageRequest.Sort, pageRequest.Order)).
		Limit(pageRequest.Size).
		Offset((pageRequest.Page - 1) * pageRequest.Size).ScanAndCount(ctx)

	if err != nil {
		return nil, err
	}

	return &types.Page[types.JobEntity]{
		Content: jobs,
		Total:   count,
		Paging:  pageRequest,
	}, nil
}

func (repo *JobRepo) FindAll(ctx context.Context) ([]types.JobEntity, error) {
	var jobs []types.JobEntity
	err := repo.db.NewSelect().Model(&jobs).Scan(ctx)
	return jobs, err
}

func (repo *JobRepo) FindByFulfillDate(ctx context.Context, date int64) ([]types.JobDto, error) {
	var jobs []types.JobDto
	err := repo.db.NewRaw("select * from v_orders_4_solver o WHERE (fulfill_date = ?) AND o.deleted_at IS NULL AND o.group_id = 1 ORDER BY o.id;", date).Scan(ctx, &jobs)
	return jobs, err
}

func (repo *JobRepo) FindAllByIdsForValidation(ctx context.Context, ids []int64) ([]types.JobDto, error) {
	var jobs []types.JobDto
	err := repo.db.NewRaw("select * from v_orders_4_solver o WHERE (o.id IN (?)) AND o.deleted_at IS NULL AND o.group_id = 1 ORDER BY o.id;",
		bun.In(ids),
	).Scan(ctx, &jobs)
	return jobs, err
}

func (repo *JobRepo) FindAllByIds(ctx context.Context, ids []int64) ([]types.JobEntity, error) {
	var jobs []types.JobEntity
	err := repo.db.NewSelect().Model(&jobs).
		Relation("Pickup").
		Relation("Delivery").
		Relation("MaterialCategory").
		Where("j.id IN (?)", bun.In(ids)).
		Scan(ctx)
	return jobs, err
}

func (repo *JobRepo) FindAllWithFilters(ctx context.Context, coordinates []types.Coordinates, names []string) ([]types.JobEntity, error) {
	var jobs []types.JobEntity
	query := repo.db.NewSelect().Model(&jobs)

	if len(coordinates) > 0 {
		coordsString := make([]string, len(coordinates))
		for i, coord := range coordinates {
			coordsString[i] = coord.String()
		}
		query.WhereGroup(" AND ", func(q *bun.SelectQuery) *bun.SelectQuery {
			for _, coord := range coordsString {
				q = q.WhereOr("CONCAT(lat, ', ', lon) = ?", coord)
			}
			return q
		})
	}

	if len(names) > 0 {
		query.Where("description IN (?)", bun.In(names))
	}

	err := query.Scan(ctx)
	return jobs, err
}

func (repo *JobRepo) FindByID(ctx context.Context, id int64) (*types.JobEntity, error) {
	jobs, err := repo.FindAllByIds(ctx, []int64{id})

	if err != nil {
		return nil, err
	}

	if len(jobs) == 0 {
		return nil, errors.New("Job with id " + strconv.FormatInt(id, 10) + " not found")
	}

	return &jobs[0], nil
}

func (repo *JobRepo) Save(ctx context.Context, job *types.JobEntity) error {
	_, err := repo.db.NewInsert().Model(job).Exec(ctx)
	return err
}
func (repo *JobRepo) Update(ctx context.Context, job *types.JobEntity) error {
	_, err := repo.db.NewUpdate().Model(job).WherePK().Exec(ctx)
	return err
}

func (repo *JobRepo) BulkUpload(ctx context.Context, jobs []types.JobEntity) ([]types.JobEntity, error) {
	err := repo.db.NewInsert().Model(&jobs).Returning("*").Scan(ctx)
	if err != nil {
		return nil, err
	}
	return jobs, nil
}
