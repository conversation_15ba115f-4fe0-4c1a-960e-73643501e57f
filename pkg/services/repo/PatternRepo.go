package repo

import (
	"context"
	"github.com/uptrace/bun"
	"loms/pkg/types"
)

type (
	PatternStore interface {
		FindAll(ctx context.Context) ([]types.PatternEntity, error)
	}

	PatternRepo struct {
		db *bun.DB
	}
)

func NewPatternRepo(db *bun.DB) *PatternRepo { return &PatternRepo{db: db} }

func (repo *PatternRepo) FindAll(ctx context.Context) ([]types.PatternEntity, error) {
	var patterns []types.PatternEntity
	err := repo.db.NewSelect().Model(&patterns).Scan(ctx)
	if err != nil {
		return nil, err
	}
	return patterns, nil
}
