package repo

import (
	"context"
	"fmt"
	"loms/pkg/types"

	"github.com/uptrace/bun"
)

type (
	DriverStore interface {
		FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.DriverEntity], error)
		FindAll(ctx context.Context) ([]types.DriverEntity, error)
		FindAllByGroupId(ctx context.Context, groupId int64) ([]types.DriverEntity, error)
		FindByID(ctx context.Context, id int64) (*types.DriverEntity, error)
		Save(ctx context.Context, driver *types.DriverEntity) error
		Update(ctx context.Context, driver *types.DriverEntity) error
		DeleteAllDriverMaterialCategoryByDriver(ctx context.Context, driverId int64) error
		SaveAllDriverMaterialCategory(ctx context.Context, driverId int64, materialCategories []int64) error
		DeleteAllDriverLeaveDatesByDriver(ctx context.Context, id int64) error
		SaveAllDriverLeaveDates(ctx context.Context, id int64, dates []int64) error
	}
	DriverRepo struct {
		db *bun.DB
	}
)

func NewDriverRepo(db *bun.DB) *DriverRepo {
	return &DriverRepo{db: db}
}

func (repo *DriverRepo) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.DriverEntity], error) {
	var entities []types.DriverEntity
	count, err := repo.db.NewSelect().Model(&entities).
		Relation("MaterialCategories").
		Relation("LeaveDates").
		Where("group_id = ?", 1).
		OrderExpr(fmt.Sprintf("%s %s", pageRequest.Sort, pageRequest.Order)).
		Limit(pageRequest.Size).
		Offset((pageRequest.Page - 1) * pageRequest.Size).ScanAndCount(ctx)

	if err != nil {
		return nil, err
	}

	return &types.Page[types.DriverEntity]{
		Content: entities,
		Total:   count,
		Paging:  pageRequest,
	}, nil
}

func (repo *DriverRepo) FindAll(ctx context.Context) ([]types.DriverEntity, error) {
	var entities []types.DriverEntity
	err := repo.db.NewSelect().Model(&entities).Relation("MaterialCategories").Scan(ctx)
	return entities, err
}

func (repo *DriverRepo) FindAllByGroupId(ctx context.Context, groupId int64) ([]types.DriverEntity, error) {
	var entities []types.DriverEntity
	err := repo.db.NewSelect().Model(&entities).Where("group_id = ?", groupId).
		Relation("Vehicles").
		Relation("MaterialCategories").
		Relation("LeaveDates").
		Scan(ctx)
	return entities, err
}

func (repo *DriverRepo) FindByID(ctx context.Context, id int64) (*types.DriverEntity, error) {
	entity := &types.DriverEntity{ID: id}
	err := repo.db.NewSelect().Model(entity).Relation("MaterialCategories").Relation("LeaveDates").WherePK().Scan(ctx)
	return entity, err
}

func (repo *DriverRepo) Save(ctx context.Context, driver *types.DriverEntity) error {
	_, err := repo.db.NewInsert().Model(driver).Exec(ctx)
	return err
}

func (repo *DriverRepo) Update(ctx context.Context, driver *types.DriverEntity) error {
	_, err := repo.db.NewUpdate().Model(driver).
		Column("nm1").
		WherePK().Exec(ctx)
	return err
}

func (repo *DriverRepo) DeleteAllDriverMaterialCategoryByDriver(ctx context.Context, driverId int64) error {
	_, err := repo.db.NewDelete().
		Model((*types.DriverMaterialCategoryEntity)(nil)).
		Where("driver_id = ?", driverId).Exec(ctx)
	return err
}

func (repo *DriverRepo) SaveAllDriverMaterialCategory(ctx context.Context, driverId int64, materialCategories []int64) error {
	var entities []types.DriverMaterialCategoryEntity
	for _, catId := range materialCategories {
		entities = append(entities, types.DriverMaterialCategoryEntity{
			DriverID: driverId,
			ItemID:   catId,
		})
	}

	if len(entities) > 0 {
		_, err := repo.db.NewInsert().Model(&entities).Exec(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}

func (repo *DriverRepo) DeleteAllDriverLeaveDatesByDriver(ctx context.Context, id int64) error {
	_, err := repo.db.NewDelete().
		Model((*types.DriverLeaveDateEntity)(nil)).
		Where("driver_id = ?", id).Exec(ctx)
	return err
}

func (repo *DriverRepo) SaveAllDriverLeaveDates(ctx context.Context, id int64, dates []int64) error {
	var entities []types.DriverLeaveDateEntity
	for _, date := range dates {
		if date == 0 {
			continue
		}
		entities = append(entities, types.DriverLeaveDateEntity{
			DriverID: id,
			Date:     date,
		})
	}

	if len(entities) > 0 {
		_, err := repo.db.NewInsert().Model(&entities).Exec(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}
