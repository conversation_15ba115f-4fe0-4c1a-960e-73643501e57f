package repo

import (
	"context"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"loms/pkg/types"
	"strconv"

	"github.com/uptrace/bun"
)

type (
	PickupStore interface {
		FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.PickupEntity], error)
		FindAllByIds(ctx context.Context, ids []int64) ([]types.PickupEntity, error)
		FindAllWithFilters(ctx context.Context, coordinates []string) ([]types.PickupEntity, error)
		FindByID(ctx context.Context, id int64) (*types.PickupEntity, error)
		Save(ctx context.Context, location *types.PickupEntity) error
		Update(ctx context.Context, location *types.PickupEntity) error
		BulkUpload(ctx context.Context, locations []types.PickupEntity) ([]types.PickupEntity, error)
	}
	PickupRepo struct {
		db *bun.DB
	}
)

func NewPickupRepo(db *bun.DB) *PickupRepo {
	return &PickupRepo{db: db}
}

func (repo *PickupRepo) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.PickupEntity], error) {
	var entities []types.PickupEntity
	if pageRequest.Sort == "Id" {
		pageRequest.Sort = "p.ID"
	}
	count, err := repo.db.NewSelect().Model(&entities).
		Relation("Location").
		OrderExpr(fmt.Sprintf("%s %s", pageRequest.Sort, pageRequest.Order)).
		Limit(pageRequest.Size).
		Offset((pageRequest.Page - 1) * pageRequest.Size).ScanAndCount(ctx)

	if err != nil {
		return nil, err
	}

	return &types.Page[types.PickupEntity]{
		Content: entities,
		Total:   count,
		Paging:  pageRequest,
	}, nil
}

func (repo *PickupRepo) FindAllByIds(ctx context.Context, ids []int64) ([]types.PickupEntity, error) {
	var locations []types.PickupEntity
	err := repo.db.NewSelect().Model(&locations).
		Relation("Location").
		Where("p.id IN (?)", bun.In(ids)).
		Scan(ctx)
	return locations, err
}

func (repo *PickupRepo) FindByID(ctx context.Context, id int64) (*types.PickupEntity, error) {
	locations, err := repo.FindAllByIds(ctx, []int64{id})

	if err != nil {
		return nil, err
	}

	if len(locations) == 0 {
		return nil, errors.New("Location with id " + strconv.FormatInt(id, 10) + " not found")
	}

	return &locations[0], nil
}

func (repo *PickupRepo) FindAllWithFilters(ctx context.Context, coordinates []string) ([]types.PickupEntity, error) {
	var locations []types.PickupEntity
	query := repo.db.NewSelect().Model(&locations).Relation("Location")

	if len(coordinates) > 0 {
		query.WhereGroup(" AND ", func(q *bun.SelectQuery) *bun.SelectQuery {
			for _, coord := range coordinates {
				q = q.WhereOr("location.lat_lon = ?", coord)
			}
			return q
		})
	}

	err := query.Scan(ctx)
	return locations, err
}

func (repo *PickupRepo) Save(ctx context.Context, location *types.PickupEntity) error {
	_, err := repo.db.NewInsert().Model(location).Exec(ctx)
	return err
}
func (repo *PickupRepo) Update(ctx context.Context, location *types.PickupEntity) error {
	_, err := repo.db.NewUpdate().Model(location).WherePK().Exec(ctx)
	return err
}

func (repo *PickupRepo) BulkUpload(ctx context.Context, locations []types.PickupEntity) ([]types.PickupEntity, error) {
	locationEntities := lo.Map(locations, func(location types.PickupEntity, _ int) types.LocationEntity {
		return *location.Location
	})

	err := repo.db.NewUpdate().
		Model(&locations).
		Column("nm").
		Bulk().
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	err = repo.db.NewUpdate().
		Model(&locationEntities).
		Column("lat_lng", "add1", "add1_kana", "add2", "add2_kana", "add3", "add3_kana").
		Bulk().
		Scan(ctx)
	if err != nil {
		return nil, err
	}

	return locations, nil
}
