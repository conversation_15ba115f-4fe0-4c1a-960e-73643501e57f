package repo

import (
	"context"
	"loms/pkg/types"

	"github.com/uptrace/bun"
)

type (
	ProcessDetailsStore interface {
		FindAll(ctx context.Context) ([]types.ProcessDetailsEntity, error)
		IsExistsActiveByType(ctx context.Context, procType int) (bool, error)
		Save(ctx context.Context, processDetails *types.ProcessDetailsEntity) (*types.ProcessDetailsEntity, error)
		Update(ctx context.Context, processDetails *types.ProcessDetailsEntity) error
	}
	ProcessDetailsRepo struct {
		db *bun.DB
	}
)

func NewProcessDetailsRepo(db *bun.DB) *ProcessDetailsRepo {
	return &ProcessDetailsRepo{db: db}
}

func (repo *ProcessDetailsRepo) FindAll(ctx context.Context) ([]types.ProcessDetailsEntity, error) {
	var entities []types.ProcessDetailsEntity
	err := repo.db.NewSelect().Model(&entities).Scan(ctx)
	return entities, err
}

func (repo *ProcessDetailsRepo) IsExistsActiveByType(ctx context.Context, procType int) (bool, error) {
	return repo.db.NewSelect().Model((*types.ProcessDetailsEntity)(nil)).
		Where("Type = ?", procType).
		Where("status = ?", types.ProcessInProgressId).
		Limit(1).
		Exists(ctx)
}
func (repo *ProcessDetailsRepo) Save(ctx context.Context, processDetails *types.ProcessDetailsEntity) (*types.ProcessDetailsEntity, error) {
	_, err := repo.db.NewInsert().Model(processDetails).Returning("*").Exec(ctx)
	if err != nil {
		return processDetails, err
	}
	return processDetails, nil
}
func (repo *ProcessDetailsRepo) Update(ctx context.Context, processDetails *types.ProcessDetailsEntity) error {
	_, err := repo.db.NewUpdate().Model(processDetails).WherePK().Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}
