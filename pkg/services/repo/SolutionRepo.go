package repo

import (
	"context"
	"github.com/samber/lo"
	"loms/pkg/types"

	"github.com/uptrace/bun"
)

type (
	SolutionStore interface {
		FindAllFeasible(ctx context.Context, runId int64) ([]types.SolutionEntity, error)
		FindSolutionByID(ctx context.Context, solutionId int64) (*types.SolutionEntity, error)
		FindById(ctx context.Context, solutionId int64) (*types.SolutionEntity, error)
		Save(ctx context.Context, solution *types.SolutionEntity) (*types.SolutionEntity, error)
		Delete(ctx context.Context, solutionId int64) error
	}
	SolutionRepo struct {
		db *bun.DB
	}
)

func NewSolutionRepo(db *bun.DB) *SolutionRepo {
	return &SolutionRepo{db: db}
}

func (repo *SolutionRepo) FindAllFeasible(ctx context.Context, runId int64) ([]types.SolutionEntity, error) {
	var solutions []types.SolutionEntity
	err := repo.db.NewSelect().Model(&solutions).
		Relation("Run").
		Where("run_id = ?", runId).Order("s.id desc").Scan(ctx)
	return solutions, err
}

/*func (repo *SolutionStore) FindById(ctx context.Context, solutionId int64) (*types.SolutionEntity, error) {
	solution := new(types.SolutionEntity)
	err := repo.db.NewSelect().Model(solution).Relation("Run").Relation("SolutionDetails").
		Relation("SolutionDetails.Order").Relation("SolutionDetails.Vehicle").Relation("SolutionDetails.Location").
		Relation("SolutionDetails.Order.MaterialCategory").
		Where("s.id = ?", solutionId).Scan(ctx)
	return solution, err
}*/

func (repo *SolutionRepo) FindSolutionByID(ctx context.Context, solutionId int64) (*types.SolutionEntity, error) {
	solution := new(types.SolutionEntity)
	err := repo.db.NewSelect().Model(solution).Relation("Constraints").Relation("Run").Where("s.id = ?", solutionId).Scan(ctx)
	return solution, err
}

func (repo *SolutionRepo) FindById(ctx context.Context, solutionId int64) (*types.SolutionEntity, error) {
	solution, err := repo.FindSolutionByID(ctx, solutionId)
	if err != nil {
		return nil, err
	}
	var solutionDetails []types.SolutionTimelineEntity
	err = repo.db.NewRaw(`select * from v_solution_details sd WHERE (sd.solution_id = ?)`, solutionId).Scan(ctx, &solutionDetails)
	solution.Timeline = lo.Map(solutionDetails, func(timeline types.SolutionTimelineEntity, _ int) *types.SolutionTimelineEntity {
		return &timeline
	})
	return solution, err
}

func (repo *SolutionRepo) Save(ctx context.Context, solution *types.SolutionEntity) (*types.SolutionEntity, error) {
	_, err := repo.db.NewInsert().Model(solution).Returning("id").Exec(ctx)
	if err != nil {
		return nil, err
	}
	/*for _, sd := range solution.SolutionDetails {
		sd.SolutionID = solution.ID
		_, err := repo.db.NewInsert().Model(sd).Exec(ctx)
		if err != nil {
			return nil, err
		}
	}*/

	for _, tl := range solution.Timeline {
		tl.SolutionID = solution.ID
		_, err := repo.db.NewInsert().Model(tl).Exec(ctx)
		if err != nil {
			return nil, err
		}
	}
	for _, c := range solution.Constraints {
		c.SolutionID = solution.ID
		_, err := repo.db.NewInsert().Model(c).Exec(ctx)
		if err != nil {
			return nil, err
		}
	}
	return solution, err
}

func (repo *SolutionRepo) Delete(ctx context.Context, solutionId int64) error {
	_, err := repo.db.NewDelete().Model(&types.SolutionEntity{}).Where("id = ?", solutionId).Exec(ctx)
	return err
}
