package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewContainer(t *testing.T) {
	assert.NotNil(t, c.Web)
	assert.NotNil(t, c.Config)
	assert.NotNil(t, c.<PERSON>)
	assert.NotNil(t, c<PERSON><PERSON><PERSON>)
	assert.NotNil(t, c.Database)
	assert.NotNil(t, c.Mail)
	assert.NotNil(t, c<PERSON>)
	assert.NotNil(t, c.<PERSON>mp<PERSON><PERSON><PERSON>)
	assert.NotNil(t, c.Tasks)
}
