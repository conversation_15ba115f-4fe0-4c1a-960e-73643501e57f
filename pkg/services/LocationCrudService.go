package services

import (
	"context"
	"fmt"
	"time"

	"loms/framework"
	"loms/framework/log"
	"loms/pkg/services/repo"
	"loms/pkg/types"

	"github.com/pkg/errors"
)

const (
	chunkSize                 = 25
	eventDistanceMatrixUpdate = "DistanceMatrixUpdateToBeUpdated"
)

type LocationCrudService struct {
	store               repo.LocationStore
	processDetailsStore repo.ProcessDetailsStore
	distanceCalculator  *GoogleMapsDistanceCalculator
	distanceStore       repo.DistanceStore
	eventBus            *framework.EventBus
}

func NewLocationCrudService(store repo.LocationStore,
	processDetailsStore repo.ProcessDetailsStore,
	distanceCalculator *GoogleMapsDistanceCalculator,
	distanceCrudPort repo.DistanceStore,
	eventBus *framework.EventBus,
) *LocationCrudService {
	return &LocationCrudService{
		store:               store,
		processDetailsStore: processDetailsStore,
		distanceCalculator:  distanceCalculator,
		distanceStore:       distanceCrudPort,
		eventBus:            eventBus,
	}
}

func (crud *LocationCrudService) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.LocationEntity], error) {
	return crud.store.FindAllPageable(ctx, pageRequest)
}

/*func (crud *LocationCrudService) FindAllWithFilters(ctx context.Context, toBeValidated []types.LocationForm) ([]types.LocationEntity, error) {
	coordinates := types.GetCoordinates(toBeValidated)
	return crud.pickupStore.FindAllWithFilters(ctx, coordinates)
}*/

func (crud *LocationCrudService) FindByID(ctx context.Context, id int64) (*types.LocationEntity, error) {
	return crud.store.FindByID(ctx, id)
}

func (crud *LocationCrudService) BulkUpload(ctx context.Context, locations []types.LocationEntity) ([]types.LocationEntity, error) {
	if err := crud.IsDistanceMatrixUpdateInProgress(ctx); err != nil {
		return nil, err
	}

	uploadedLocations, err := crud.store.BulkUpload(ctx, locations)
	if err != nil {
		return nil, errors.Wrap(err, "failed to bulk upload locations")
	}

	if err := crud.StartDistanceMatrixUpdateProcessAndPublish(ctx, uploadedLocations); err != nil {
		return nil, errors.Wrap(err, "failed to start distance matrix update process")
	}

	return uploadedLocations, nil
}

func (crud *LocationCrudService) IsDistanceMatrixUpdateInProgress(ctx context.Context) error {
	isInProgress, err := crud.processDetailsStore.IsExistsActiveByType(ctx, types.DistanceMatrixUpdateProcessType)
	if err != nil {
		return errors.Wrap(err, "failed to check if distance matrix update is in progress")
	}
	if isInProgress {
		return types.ErrDistanceMatrixUpdateInProgress
	}
	return nil
}

func (crud *LocationCrudService) StartDistanceMatrixUpdateProcessAndPublish(ctx context.Context, savedLocations []types.LocationEntity) error {
	processDetails, err := crud.processDetailsStore.Save(ctx, &types.ProcessDetailsEntity{
		Type:        types.DistanceMatrixUpdateProcessType,
		Description: fmt.Sprintf("DM start: %s", time.Now().String()),
		Start:       time.Now(),
	})
	if err != nil {
		return errors.Wrap(err, "failed to save process details")
	}

	eventData := map[string]interface{}{
		"savedLocations": savedLocations,
		"process":        *processDetails,
	}
	crud.eventBus.Publish(eventDistanceMatrixUpdate, eventData)
	return nil
}

func (crud *LocationCrudService) HandleDistanceMatrixUpdateEvent(eventData map[string]interface{}) {
	go func() {
		if err := crud.processDistanceMatrix(eventData); err != nil {
			log.Default().Error("Error processing distance matrix update: %v", err)
		}
	}()
}

func (crud *LocationCrudService) processDistanceMatrix(eventData map[string]interface{}) error {
	savedLocations := eventData["savedLocations"].([]types.LocationEntity)
	processDetails := eventData["process"].(types.ProcessDetailsEntity)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Hour)
	defer cancel()

	allLocations, err := crud.store.FindAllWhoseLatLngIsNotEmpty(ctx)
	if err != nil {
		return errors.Wrap(err, "failed to find all locations")
	}

	distanceEntities, err := crud.calculateDistances(ctx, savedLocations, allLocations)
	if err != nil {
		return errors.Wrap(err, "failed to calculate distances")
	}

	uniqueEntities := removeDuplicateEntities(distanceEntities)
	/*if err := crud.distanceStore.SaveBatch(ctx, uniqueEntities); err != nil {
		return errors.Wrap(err, "failed to save distance entities")
	}*/

	log.Default().Info("Distance entities: %v, unique: %v", len(uniqueEntities), len(uniqueEntities))

	processDetails.Description += fmt.Sprintf(", end: %s", time.Now().String())
	processDetails.Status = types.ProcessCompletedId
	processDetails.End = time.Now()
	return crud.processDetailsStore.Update(ctx, &processDetails)
}

func (crud *LocationCrudService) calculateDistances(ctx context.Context, savedLocations, allLocations []types.LocationEntity) ([]types.DistanceEntity, error) {
	var distanceEntities []types.DistanceEntity
	for _, from := range savedLocations {
		chunks := getChunks(allLocations, chunkSize)
		var chunkDistanceEntities []types.DistanceEntity
		for _, chunk := range chunks {
			if distances, err := crud.distanceCalculator.CalculateDistanceMatrix(ctx, chunk, []types.LocationEntity{from}, "driving"); err == nil {
				distanceEntities = append(distanceEntities, distances...)
				chunkDistanceEntities = append(chunkDistanceEntities, distances...)
			} else {
				return nil, errors.Wrap(err, "failed to calculate distance matrix")
			}

			if distances, err := crud.distanceCalculator.CalculateDistanceMatrix(ctx, []types.LocationEntity{from}, chunk, "driving"); err == nil {
				distanceEntities = append(distanceEntities, distances...)
				chunkDistanceEntities = append(chunkDistanceEntities, distances...)
			} else {
				return nil, errors.Wrap(err, "failed to calculate distance matrix")
			}
		}

		uniqueEntities := removeDuplicateEntities(chunkDistanceEntities)
		log.Default().Info("Distances being saved: %v, unique: %v", len(chunkDistanceEntities), len(uniqueEntities))
		if err := crud.distanceStore.SaveBatch(ctx, uniqueEntities); err != nil {
			return nil, errors.Wrap(err, "failed to save distance entities")
		}
	}
	return distanceEntities, nil
}

func getChunks(slice []types.LocationEntity, chunkSize int) [][]types.LocationEntity {
	var chunks [][]types.LocationEntity
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

func (crud *LocationCrudService) Upsert(ctx context.Context, toBeSavedLocation *types.LocationEntity) error {
	if toBeSavedLocation.ID != 0 {
		existingFromDB, err := crud.store.FindByID(ctx, toBeSavedLocation.ID)
		if err != nil {
			return errors.Wrap(err, "failed to find location by ID")
		}

		if existingFromDB.LatLng != toBeSavedLocation.LatLng {
			if err := crud.IsDistanceMatrixUpdateInProgress(ctx); err != nil {
				return err
			}
		}

		return crud.store.Update(ctx, toBeSavedLocation)
	}

	if err := crud.IsDistanceMatrixUpdateInProgress(ctx); err != nil {
		return err
	}

	return crud.store.Save(ctx, toBeSavedLocation)
}

func removeDuplicateEntities(entities []types.DistanceEntity) []types.DistanceEntity {
	unique := make(map[string]types.DistanceEntity)
	for _, entity := range entities {
		key := fmt.Sprintf("%d-%d", entity.FromLocId, entity.ToLocId)
		unique[key] = entity
	}
	result := make([]types.DistanceEntity, 0, len(unique))
	for _, entity := range unique {
		result = append(result, entity)
	}
	return result
}
