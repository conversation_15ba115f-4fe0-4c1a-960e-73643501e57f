package services

import (
	"context"
	"loms/pkg/services/repo"
	"loms/pkg/types"
)

type VehicleCrudService struct {
	store         repo.VehicleStore
	categoryStore repo.MaterialCategoryStore
}

func NewVehicleCrudService(store repo.VehicleStore, catRepo repo.MaterialCategoryStore) *VehicleCrudService {
	return &VehicleCrudService{store: store, categoryStore: catRepo}
}

func (crud *VehicleCrudService) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.VehicleEntity], error) {
	return crud.store.FindAllPageable(ctx, pageRequest)
}

func (crud *VehicleCrudService) FindAll(ctx context.Context) ([]types.VehicleEntity, error) {
	return crud.store.FindAll(ctx)
}

func (crud *VehicleCrudService) FindAllCategories(ctx context.Context) ([]types.ItemEntity, error) {
	return crud.categoryStore.FindAll(ctx)
}

func (crud *VehicleCrudService) FindByID(ctx context.Context, id int64) (*types.VehicleEntity, error) {
	return crud.store.FindByID(ctx, id)
}

func (crud *VehicleCrudService) Upsert(ctx context.Context, entity *types.VehicleEntity) error {
	if entity.ID != 0 {

		if err := crud.store.Update(ctx, entity); err != nil {
			return err
		}

		if err := crud.store.DeleteAllVehicleMaterialCategoryByVehicle(ctx, entity.ID); err != nil {
			return err
		}

		if err := crud.store.SaveAllVehicleMaterialCategory(ctx, entity.ID, entity.CanHandle); err != nil {
			return err
		}
		return nil
	}
	return crud.store.Save(ctx, entity)
}
