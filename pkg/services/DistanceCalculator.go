package services

import (
	"context"
	"fmt"
	"log"
	"loms/pkg/services/api"
	"loms/pkg/types"
	"strings"
	"time"
)

type GoogleMapsDistanceCalculator struct {
	googleMapsExternalService *api.GoogleMapsApi
}

func NewGoogleMapsDistanceCalculator(googleMapsExternalService *api.GoogleMapsApi) *GoogleMapsDistanceCalculator {
	return &GoogleMapsDistanceCalculator{googleMapsExternalService: googleMapsExternalService}
}

func (service *GoogleMapsDistanceCalculator) CalculateDistanceMatrix(ctx context.Context,
	from, to []types.LocationEntity, mode string) ([]types.DistanceEntity, error) {
	origins := locationsToString(from)
	destinations := locationsToString(to)

	response, err := service.googleMapsExternalService.GetDistanceMatrix(ctx, mode, origins, destinations)
	if err != nil {
		return nil, fmt.Errorf("failed to get distance matrix: %w", err)
	}

	distanceEntities := make([]types.DistanceEntity, 0, len(from)*len(to))
	for fromIdx, origin := range from {
		for toIdx, destination := range to {
			element := response.Rows[fromIdx].Elements[toIdx]

			if element.Status != "OK" {
				log.Printf("Error calculating distance for %v(%v) to %v(%v): %v", origin.ID, origin.LatLng, destination.ID, destination.LatLng, element.Status)
				continue
			}

			duration := element.Duration.Value
			distanceEntities = append(distanceEntities, types.DistanceEntity{
				FromLocId: origin.ID,
				ToLocId:   destination.ID,
				Duration:  duration * 1000,
				CreatedAt: time.Now().Unix(),
			})
		}
	}
	return distanceEntities, nil
}

func locationsToString(locations []types.LocationEntity) string {
	coords := make([]string, len(locations))
	for i, loc := range locations {
		coords[i] = loc.LatLng
	}
	return strings.Join(coords, "|")
}
