package auth

import (
	"context"
	"errors"
	"loms/pkg/db/sqlc"
	"loms/pkg/services"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/stretchr/testify/assert"
)

func TestAuthClient_Auth(t *testing.T) {
	assertNoAuth := func() {
		_, err := services.c.Auth.GetAuthenticatedUserID(services.ctx)
		assert.True(t, errors.Is(err, NotAuthenticatedError{}))
		_, err = services.c.Auth.GetAuthenticatedUser(services.ctx)
		assert.True(t, errors.Is(err, NotAuthenticatedError{}))
	}

	assertNoAuth()

	err := services.c.Auth.Login(services.ctx, services.usr.ID)
	require.NoError(t, err)

	uid, err := services.c.Auth.GetAuthenticatedUserID(services.ctx)
	require.NoError(t, err)
	assert.Equal(t, services.usr.ID, uid)

	u, err := services.c.Auth.GetAuthenticatedUser(services.ctx)
	require.NoError(t, err)
	assert.Equal(t, u.ID, services.usr.ID)

	err = services.c.Auth.Logout(services.ctx)
	require.NoError(t, err)

	assertNoAuth()
}

func TestAuthClient_PasswordHashing(t *testing.T) {
	pw := "testcheckpassword"
	hash, err := services.c.Auth.HashPassword(pw)
	assert.NoError(t, err)
	assert.NotEqual(t, hash, pw)
	err = services.c.Auth.CheckPassword(pw, hash)
	assert.NoError(t, err)
}

func TestAuthClient_GeneratePasswordResetToken(t *testing.T) {
	token, pt, err := services.c.Auth.GeneratePasswordResetToken(services.ctx, services.usr.ID)
	require.NoError(t, err)
	assert.Len(t, token, services.c.Config.App.PasswordToken.Length)
	assert.NoError(t, services.c.Auth.CheckPassword(token, pt.Hash))
}

func TestAuthClient_GetValidPasswordToken(t *testing.T) {
	// Check that a fake token is not valid
	_, err := services.c.Auth.GetValidPasswordToken(services.ctx, services.usr.ID, 1, "faketoken")
	assert.Error(t, err)

	// Generate a valid token and check that it is returned
	token, pt, err := services.c.Auth.GeneratePasswordResetToken(services.ctx, services.usr.ID)
	require.NoError(t, err)
	pt2, err := services.c.Auth.GetValidPasswordToken(services.ctx, services.usr.ID, pt.ID, token)
	require.NoError(t, err)
	assert.Equal(t, pt.ID, pt2.ID)

	// Expire the token by pushing the date far enough back
	count, err := services.c.Db.Test_UpdatePasswordTokenCreatedAt(context.Background(),
		sqlc.Test_UpdatePasswordTokenCreatedAtParams{
			CreatedAt: time.Now().Add(-(services.c.Config.App.PasswordToken.Expiration + time.Hour)),
			ID:        pt.ID,
		})
	require.NoError(t, err)
	require.Equal(t, 1, count.RowsAffected)

	// Expired tokens should not be valid
	_, err = services.c.Auth.GetValidPasswordToken(services.ctx, services.usr.ID, pt.ID, token)
	assert.Error(t, err)
}

func TestAuthClient_DeletePasswordTokens(t *testing.T) {
	// Create three tokens for the user
	for i := 0; i < 3; i++ {
		_, _, err := services.c.Auth.GeneratePasswordResetToken(services.ctx, services.usr.ID)
		require.NoError(t, err)
	}

	// Delete all tokens for the user
	err := services.c.Auth.DeletePasswordTokens(services.ctx, services.usr.ID)
	require.NoError(t, err)

	// Check that no tokens remain
	count, err := services.c.Db.Test_CountPasswordTokensByUser(context.Background(), services.usr.ID)

	require.NoError(t, err)
	assert.Equal(t, int64(0), count)
}

func TestAuthClient_RandomToken(t *testing.T) {
	length := services.c.Config.App.PasswordToken.Length
	a, err := services.c.Auth.RandomToken(length)
	require.NoError(t, err)
	b, err := services.c.Auth.RandomToken(length)
	require.NoError(t, err)
	assert.Len(t, a, length)
	assert.Len(t, b, length)
	assert.NotEqual(t, a, b)
}

func TestAuthClient_EmailVerificationToken(t *testing.T) {
	t.Run("valid token", func(t *testing.T) {
		email := "<EMAIL>"
		token, err := services.c.Auth.GenerateEmailVerificationToken(email)
		require.NoError(t, err)

		tokenEmail, err := services.c.Auth.ValidateEmailVerificationToken(token)
		require.NoError(t, err)
		assert.Equal(t, email, tokenEmail)
	})

	t.Run("invalid token", func(t *testing.T) {
		badToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************.ScJCpfEEzlilKfRs_aVouzwPNKI28M3AIm-hyImQHUQ"
		_, err := services.c.Auth.ValidateEmailVerificationToken(badToken)
		assert.Error(t, err)
	})

	t.Run("expired token", func(t *testing.T) {
		services.c.Config.App.EmailVerificationTokenExpiration = -time.Hour
		email := "<EMAIL>"
		token, err := services.c.Auth.GenerateEmailVerificationToken(email)
		require.NoError(t, err)

		_, err = services.c.Auth.ValidateEmailVerificationToken(token)
		assert.Error(t, err)

		services.c.Config.App.EmailVerificationTokenExpiration = time.Hour * 12
	})
}
