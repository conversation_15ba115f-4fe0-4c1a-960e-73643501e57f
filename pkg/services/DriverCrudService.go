package services

import (
	"context"
	"loms/pkg/services/repo"
	"loms/pkg/types"
)

// DriverCrudService provides CRUD operations for drivers.
type DriverCrudService struct {
	store         repo.DriverStore
	categoryStore repo.MaterialCategoryStore
}

// NewDriverCrudService creates a new instance of DriverCrudService.
func NewDriverCrudService(repo repo.DriverStore, catRepo repo.MaterialCategoryStore) *DriverCrudService {
	return &DriverCrudService{store: repo, categoryStore: catRepo}
}

// FindAllPageable retrieves a pageable list of drivers.
func (crud *DriverCrudService) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.DriverEntity], error) {
	return crud.store.FindAllPageable(ctx, pageRequest)
}

// FindAll retrieves all drivers.
func (crud *DriverCrudService) FindAll(ctx context.Context) ([]types.DriverEntity, error) {
	return crud.store.FindAll(ctx)
}

// FindAllCategories retrieves all material categories.
func (crud *DriverCrudService) FindAllCategories(ctx context.Context) ([]types.ItemEntity, error) {
	return crud.categoryStore.FindAll(ctx)
}

// FindByID retrieves a driver by its ID.
func (crud *DriverCrudService) FindByID(ctx context.Context, id int64) (*types.DriverEntity, error) {
	return crud.store.FindByID(ctx, id)
}

// Upsert inserts or updates a driver entity.
func (crud *DriverCrudService) Upsert(ctx context.Context, entity *types.DriverEntity) error {
	if entity.ID == 0 {
		return crud.store.Save(ctx, entity)
	}

	if err := crud.store.Update(ctx, entity); err != nil {
		return err
	}

	if err := crud.store.DeleteAllDriverLeaveDatesByDriver(ctx, entity.ID); err != nil {
		return err
	}

	if err := crud.store.SaveAllDriverLeaveDates(ctx, entity.ID, entity.LeaveTakenDates); err != nil {
		return err
	}

	if err := crud.store.DeleteAllDriverMaterialCategoryByDriver(ctx, entity.ID); err != nil {
		return err
	}

	return crud.store.SaveAllDriverMaterialCategory(ctx, entity.ID, entity.CanHandle)
}
