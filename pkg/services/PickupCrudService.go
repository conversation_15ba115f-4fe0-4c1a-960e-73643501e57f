package services

import (
	"context"
	"loms/pkg/services/repo"
	"loms/pkg/types"
)

type PickupCrudService struct {
	store repo.PickupStore
}

func NewPickupCrudService(store repo.PickupStore) *PickupCrudService {
	return &PickupCrudService{
		store: store,
	}
}

func (crud *PickupCrudService) FindAllPageable(ctx context.Context, pageRequest *types.PageRequest) (*types.Page[types.PickupEntity], error) {
	return crud.store.FindAllPageable(ctx, pageRequest)
}

func (crud *PickupCrudService) FindByID(ctx context.Context, id int64) (*types.PickupEntity, error) {
	return crud.store.FindByID(ctx, id)
}

// Upsert inserts or updates a driver entity.
func (crud *PickupCrudService) Upsert(ctx context.Context, entity *types.PickupEntity) error {
	if entity.ID == 0 {
		return crud.store.Save(ctx, entity)
	}

	return crud.store.Update(ctx, entity)
}
