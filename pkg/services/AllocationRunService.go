package services

import (
	"context"

	"loms/pkg/services/repo"
	"loms/pkg/types"
	"time"
)

type AllocationRunService struct {
	store repo.AllocationRunStore
}

func NewAllocationRunService(store repo.AllocationRunStore) *AllocationRunService {
	return &AllocationRunService{store: store}
}

func (s AllocationRunService) Save(ctx context.Context, input types.AllocationSaveInput) (int64, error) {
	entity, err := s.store.Save(ctx, &types.AllocationRunEntity{
		Status:             input.Status,
		CreatedAt:          time.Now().Unix(),
		FulfillDate:        input.FulfillDate,
		WithHistory:        input.WithHistory,
		WithEvenAllocation: input.WithEvenAllocation,
	})
	if err != nil {
		return 0, err
	}
	return entity.ID, nil
}

func (s AllocationRunService) Update(ctx context.Context, input *types.AllocationUpdateInput) (*types.AllocationRunEntity, error) {
	allocationRunFromDB, err := s.store.FindByID(ctx, input.ID)
	if err != nil {
		return nil, err
	}

	allocationRunUpdated, err := s.store.Update(ctx, &types.AllocationRunEntity{
		ID:                 input.ID,
		CreatedAt:          allocationRunFromDB.CreatedAt,
		UpdatedAt:          time.Now().Unix(),
		Status:             input.Status,
		FulfillDate:        allocationRunFromDB.FulfillDate,
		WithHistory:        allocationRunFromDB.WithHistory,
		WithEvenAllocation: allocationRunFromDB.WithEvenAllocation,
	})

	if err != nil {
		return nil, err
	}
	return allocationRunUpdated, nil
}
