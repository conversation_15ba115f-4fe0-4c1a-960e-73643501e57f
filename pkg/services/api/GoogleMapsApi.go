package api

import (
	"context"
	"encoding/json"
	"fmt"
	"loms/pkg/types"
	"net/http"
	"net/url"
	"time"
)

type GoogleMapsApi struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
}

func NewGoogleMapsApi(baseURL, apiKey string, httpClient *http.Client) *GoogleMapsApi {
	return &GoogleMapsApi{
		baseURL:    baseURL,
		apiKey:     apiKey,
		httpClient: httpClient,
	}
}

func (c *GoogleMapsApi) GetDistanceMatrix(ctx context.Context, mode, origins, destinations string) (*types.DistanceResponse, error) {
	endpoint := fmt.Sprintf("%s/api/distancematrix/json", c.baseURL)
	params := url.Values{
		"mode":         {mode},
		"origins":      {origins},
		"destinations": {destinations},
		"key":          {c.apiKey},
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, 5*time.Minute)
	defer cancel()

	req, err := http.NewRequestWithContext(timeoutCtx, http.MethodGet, fmt.Sprintf("%s?%s", endpoint, params.Encode()), nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to retrieve distance matrix: %s", resp.Status)
	}

	var distanceResponse types.DistanceResponse
	if err := json.NewDecoder(resp.Body).Decode(&distanceResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &distanceResponse, nil
}
