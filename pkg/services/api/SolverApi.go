package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"loms/pkg/types"
	"net/http"
)

type SolverApi struct {
	solverUrl  string
	httpClient *http.Client
}

func NewSolverApi(solverUrl string, httpClient *http.Client) *SolverApi {
	return &SolverApi{
		solverUrl:  solverUrl,
		httpClient: httpClient,
	}
}

// Solve sends a POST request with a Problem payload and returns the Run ID
func (s *SolverApi) Solve(problem types.Problem) error {
	jsonData, err := json.Marshal(problem)
	if err != nil {
		return fmt.Errorf("could not marshal problem to JSON: %w", err)
	}

	req, err := http.NewRequest("POST", s.solverUrl+"/api/v1/solutions/solve", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("could not create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("received non-OK HTTP status: %d %s", resp.StatusCode, http.StatusText(resp.StatusCode))
	}
	return nil
}

func (s *SolverApi) ValidateSolutionChange(problem types.SolutionChangeValidationRequest) (*types.SolutionChangeValidationResponse, error) {
	jsonData, err := json.Marshal(problem)
	if err != nil {
		return nil, fmt.Errorf("could not marshal problem to JSON: %w", err)
	}

	req, err := http.NewRequest("POST", s.solverUrl+"/api/v1/solutions/validate-solution-change", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("could not create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("could not send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("received non-OK HTTP status: %d %s", resp.StatusCode, http.StatusText(resp.StatusCode))
	}
	solutionValidationResponse := types.SolutionChangeValidationResponse{}
	if err := json.NewDecoder(resp.Body).Decode(&solutionValidationResponse); err != nil {
		return nil, fmt.Errorf("could not decode response: %w", err)
	}

	return &solutionValidationResponse, nil
}
