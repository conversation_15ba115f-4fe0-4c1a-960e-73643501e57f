package services

import (
	"context"
	"fmt"
	"github.com/samber/lo"
	"log"
	"loms/framework"
	"loms/pkg/services/repo"
	"loms/pkg/types"
	"time"
)

type CoordinatesUpdateService struct {
	pickupStore         repo.PickupStore
	destinationStore    repo.DestinationAreaStore
	processDetailsStore repo.ProcessDetailsStore
	eventBus            *framework.EventBus
}

func NewCoordinatesUpdateService(pickupStore repo.PickupStore,
	destinationStore repo.DestinationAreaStore,
	processDetailsStore repo.ProcessDetailsStore,
	eventBus *framework.EventBus,
) *CoordinatesUpdateService {
	return &CoordinatesUpdateService{
		pickupStore:         pickupStore,
		destinationStore:    destinationStore,
		processDetailsStore: processDetailsStore,
		eventBus:            eventBus,
	}
}

func (s *CoordinatesUpdateService) PickupNameAddressCoordinatesBulkUpload(ctx context.Context, locations []types.PickupEntity) ([]types.PickupEntity, error) {
	if err := s.IsDistanceMatrixUpdateInProgress(ctx); err != nil {
		return nil, err
	}

	existingLocations, err := s.pickupStore.FindAllByIds(ctx, lo.Map(locations, func(location types.PickupEntity, _ int) int64 {
		return location.ID
	}))
	if err != nil {
		return nil, err
	}

	uploadedLocations, err := s.pickupStore.BulkUpload(ctx, locations)
	if err != nil {
		return nil, err
	}

	toBePopulatedInDistanceMatrix := lo.Filter(locations, func(location types.PickupEntity, _ int) bool {
		return lo.ContainsBy(existingLocations, func(existingLocation types.PickupEntity) bool {
			idSame := existingLocation.Location.ID == location.Location.ID
			latLngDifferent := existingLocation.Location.LatLng != location.Location.LatLng
			log.Printf("idSame: %v, latLngDifferent: %v", idSame, latLngDifferent)
			return idSame && latLngDifferent
		})
	})
	locationsToBePopulatedInDistanceMatrix := lo.Map(toBePopulatedInDistanceMatrix, func(location types.PickupEntity, _ int) types.LocationEntity {
		return *location.Location
	})

	if err := s.StartDistanceMatrixUpdateProcessAndPublish(ctx, locationsToBePopulatedInDistanceMatrix); err != nil {
		return nil, err
	}

	return uploadedLocations, nil
}

func (s *CoordinatesUpdateService) DestinationNameAddressCoordinatesBulkUpload(ctx context.Context, locations []types.DestinationAreaEntity) ([]types.DestinationAreaEntity, error) {
	if err := s.IsDistanceMatrixUpdateInProgress(ctx); err != nil {
		return nil, err
	}

	existingLocations, err := s.destinationStore.FindAllByIds(ctx, lo.Map(locations, func(location types.DestinationAreaEntity, _ int) int64 {
		return location.ID
	}))
	if err != nil {
		return nil, err
	}

	uploadedLocations, err := s.destinationStore.BulkUpload(ctx, locations)
	if err != nil {
		return nil, err
	}

	toBePopulatedInDistanceMatrix := lo.Filter(locations, func(location types.DestinationAreaEntity, _ int) bool {
		return lo.ContainsBy(existingLocations, func(existingLocation types.DestinationAreaEntity) bool {
			idSame := existingLocation.Location.ID == location.Location.ID
			latLngDifferent := existingLocation.Location.LatLng != location.Location.LatLng
			log.Printf("idSame: %v, latLngDifferent: %v", idSame, latLngDifferent)
			return idSame && latLngDifferent
		})
	})
	locationsToBePopulatedInDistanceMatrix := lo.Map(toBePopulatedInDistanceMatrix, func(location types.DestinationAreaEntity, _ int) types.LocationEntity {
		return *location.Location
	})

	if err := s.StartDistanceMatrixUpdateProcessAndPublish(ctx, locationsToBePopulatedInDistanceMatrix); err != nil {
		return nil, err
	}

	return uploadedLocations, nil

}

func (s *CoordinatesUpdateService) IsDistanceMatrixUpdateInProgress(ctx context.Context) error {
	isInProgress, err := s.processDetailsStore.IsExistsActiveByType(ctx, 1)
	if err != nil {
		return err
	}
	if isInProgress {
		return types.ErrDistanceMatrixUpdateInProgress
	}
	return nil
}

func (s *CoordinatesUpdateService) StartDistanceMatrixUpdateProcessAndPublish(ctx context.Context, savedLocations []types.LocationEntity) error {
	processDetails, err := s.processDetailsStore.Save(ctx, &types.ProcessDetailsEntity{
		Type:        1,
		Description: fmt.Sprintf("DM start: %s", time.Now().String()),
		Start:       time.Now(),
	})
	if err != nil {
		return err
	}

	eventData := map[string]interface{}{
		"savedLocations": savedLocations,
		"process":        *processDetails, // Dereference the pointer to get the actual value
	}
	s.eventBus.Publish("DistanceMatrixUpdateToBeUpdated", eventData)
	return nil
}
