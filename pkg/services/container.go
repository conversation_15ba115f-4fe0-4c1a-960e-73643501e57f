package services

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"loms/framework"
	"loms/framework/log"
	"loms/pkg/db/migration"
	"loms/pkg/services/api"
	"loms/pkg/services/auth"
	"loms/pkg/services/repo"
	"loms/pkg/types"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/pressly/goose/v3"
	"github.com/pressly/goose/v3/database"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/sqlitedialect"
	"github.com/uptrace/bun/extra/bundebug"

	"github.com/mikestefanello/backlite"

	"loms/config"

	"github.com/labstack/echo/v4"
	_ "github.com/mattn/go-sqlite3"
)

// Container contains all services used by the application and provides an easy way to handle dependency
// injection including within tests
type Container struct {
	// Validator stores a validator
	Validator *framework.Validator

	// Web stores the web framework
	Web *echo.Echo

	// Config stores the application configuration
	Config *config.Config

	// Cache contains the cache client
	Cache *framework.CacheClient

	// Database stores the connection to the database
	Database *sql.DB

	// Db from Bun
	Db *bun.DB

	// Mail stores an email sending client
	Mail *framework.MailClient

	// Auth stores an authentication client
	Auth *auth.Client

	// TemplateRenderer stores a service to easily render and cache templates
	TemplateRenderer *framework.TemplateRenderer

	// Tasks stores the task client
	Tasks *backlite.Client

	EventBus                     *framework.EventBus
	AllocationRunService         *AllocationRunService
	SolutionService              *SolutionService
	LocationCrudService          *LocationCrudService
	PickupCrudService            *PickupCrudService
	CoordinatesUpdateService     *CoordinatesUpdateService
	SolverApi                    *api.SolverApi
	GoogleMapsApi                *api.GoogleMapsApi
	GoogleMapsDistanceCalculator *GoogleMapsDistanceCalculator

	MaterialCategoryCrudService *MaterialCategoryCrudService
	DriverCrudService           *DriverCrudService
	VehicleCrudService          *VehicleCrudService
	JobCrudService              *JobCrudService
	AppVersion                  string
}

var solutionStore repo.SolutionStore
var jobStore repo.JobStore
var vehicleStore repo.VehicleStore
var allocationRunStore repo.AllocationRunStore
var locationStore repo.LocationStore
var pickupStore repo.PickupStore
var destinationAreaStore repo.DestinationAreaStore
var distanceStore repo.DistanceStore
var patternStore repo.PatternStore
var processDetailsStore repo.ProcessDetailsStore
var materialCategoryStore repo.MaterialCategoryStore
var driverStore repo.DriverStore

// NewContainer creates and initializes a new Container
func NewContainer(appVer string) *Container {
	c := new(Container)
	c.AppVersion = appVer
	c.initConfig()
	c.initValidator()
	c.initWeb()
	c.initCache()
	c.initDatabase()
	c.applyMigrations()
	c.initAuth()
	c.initTemplateRenderer()
	c.initMail()
	c.initTasks()
	c.initRepos()
	c.initServices()
	return c
}

// Shutdown shuts the Container down and disconnects all connections.
// If the task runner was started, cancel the context to shut it down prior to calling this.
func (c *Container) Shutdown() error {
	if err := c.Database.Close(); err != nil {
		return err
	}
	if err := c.Db.Close(); err != nil {
		return err
	}
	c.Cache.Close()

	return nil
}

// initConfig initializes configuration
func (c *Container) initConfig() {
	cfg, err := config.GetConfig()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}
	c.Config = &cfg

	// Configure logging
	switch cfg.App.Environment {
	case config.EnvProduction:
		slog.SetLogLoggerLevel(slog.LevelInfo)
	default:
		slog.SetLogLoggerLevel(slog.LevelDebug)
	}
}

// initValidator initializes the validator
func (c *Container) initValidator() {
	c.Validator = framework.NewValidator()
}

// initWeb initializes the web framework
func (c *Container) initWeb() {
	c.Web = echo.New()
	c.Web.HideBanner = true
	c.Web.Validator = c.Validator
}

// initCache initializes the cache
func (c *Container) initCache() {
	store, err := framework.NewInMemoryCache(c.Config.Cache.Capacity)
	if err != nil {
		panic(err)
	}

	c.Cache = framework.NewCacheClient(store)
}

// initDatabase initializes the database
func (c *Container) initDatabase() {
	var err error
	var connection string

	switch c.Config.App.Environment {
	case config.EnvTest:
		// TODO: Drop/recreate the DB, if this isn't in memory?
		connection = c.Config.Database.TestConnection
	default:
		connection = c.Config.Database.Connection
	}

	c.Database, err = openDB(c.Config.Database.Driver, connection)
	if err != nil {
		panic(err)
	}

	c.Db = bun.NewDB(c.Database, sqlitedialect.New())
	if c.Config.Database.LogSQL {
		c.Db.AddQueryHook(bundebug.NewQueryHook(bundebug.WithVerbose(true)))
	}

	// Register many-to-many models so bun can better recognize m2m relation.
	// This should be done before you use the model for the first time.
	c.Db.RegisterModel((*types.VehicleMaterialCategoryEntity)(nil))
	c.Db.RegisterModel((*types.DriverMaterialCategoryEntity)(nil))
	c.Db.RegisterModel((*types.DriverVehicle)(nil))

}

func (c *Container) applyMigrations() {
	if !c.Config.Database.MigrateOnStartup {
		return
	}
	var err error
	provider, err := goose.NewProvider(database.DialectSQLite3, c.Database, migration.Embed)
	if err != nil {
		panic(err)
	}
	// List migration sources the provider is aware of.
	log.Default().Info("\n=== migration list ===")
	sources := provider.ListSources()
	for _, s := range sources {
		log.Default().Info(fmt.Sprintf("%-3s %-2v %v\n", s.Type, s.Version, filepath.Base(s.Path)))
	}

	// List status of migrations before applying them.
	ctx := context.Background()
	stats, err := provider.Status(ctx)
	if err != nil {
		log.Default().Error(err.Error())
	}
	log.Default().Info("\n=== migration status ===")
	for _, s := range stats {
		log.Default().Info(fmt.Sprintf("%-3s %-2v %v\n", s.Source.Type, s.Source.Version, s.State))
	}

	log.Default().Info("\n=== log migration output  ===")
	results, err := provider.Up(ctx)
	if err != nil {
		log.Default().Error(err.Error())
	}
	log.Default().Info("\n=== migration results  ===")
	for _, r := range results {
		log.Default().Info(fmt.Sprintf("%-3s %-2v done: %v\n", r.Source.Type, r.Source.Version, r.Duration))
	}
}

// initAuth initializes the authentication client
func (c *Container) initAuth() {
	c.Auth = auth.NewAuthClient(c.Config, c.Db)
}

// initTemplateRenderer initializes the template renderer
func (c *Container) initTemplateRenderer() {
	c.TemplateRenderer = framework.NewTemplateRenderer(c.Config, c.Cache, c.Web, c.AppVersion, string(c.Config.App.Environment))
}

// initMail initialize the mail client
func (c *Container) initMail() {
	var err error
	c.Mail, err = framework.NewMailClient(c.Config, c.TemplateRenderer)
	if err != nil {
		panic(fmt.Sprintf("failed to create mail client: %v", err))
	}
}

// initTasks initializes the task client
func (c *Container) initTasks() {
	var err error
	// You could use a separate database for tasks, if you'd like. but using one
	// makes transaction support easier
	c.Tasks, err = backlite.NewClient(backlite.ClientConfig{
		DB:              c.Database,
		Logger:          log.Default(),
		NumWorkers:      c.Config.Tasks.Goroutines,
		ReleaseAfter:    c.Config.Tasks.ReleaseAfter,
		CleanupInterval: c.Config.Tasks.CleanupInterval,
	})

	if err != nil {
		panic(fmt.Sprintf("failed to create task client: %v", err))
	}

	if err = c.Tasks.Install(); err != nil {
		panic(fmt.Sprintf("failed to install task schema: %v", err))
	}
}

func (c *Container) initRepos() {
	solutionStore = repo.NewSolutionRepo(c.Db)
	jobStore = repo.NewJobRepo(c.Db)
	vehicleStore = repo.NewVehicleRepo(c.Db)
	allocationRunStore = repo.NewAllocationRunRepo(c.Db)
	locationStore = repo.NewLocationRepo(c.Db)
	pickupStore = repo.NewPickupRepo(c.Db)
	destinationAreaStore = repo.NewDestinationAreaRepo(c.Db)
	distanceStore = repo.NewDistanceRepo(c.Db)
	patternStore = repo.NewPatternRepo(c.Db)
	processDetailsStore = repo.NewProcessDetailsRepo(c.Db)
	materialCategoryStore = repo.NewMaterialCategoryRepo(c.Db)
	driverStore = repo.NewDriverRepo(c.Db)

}

func (c *Container) initServices() {

	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	c.configureEventBus()

	c.SolverApi = api.NewSolverApi(c.Config.App.SolverApiUrl, httpClient)
	c.GoogleMapsApi = api.NewGoogleMapsApi(c.Config.App.GoogleMapsBaseUrl, c.Config.App.GoogleMapsApiKey, httpClient)
	c.GoogleMapsDistanceCalculator = NewGoogleMapsDistanceCalculator(c.GoogleMapsApi)
	c.LocationCrudService = NewLocationCrudService(locationStore, processDetailsStore, c.GoogleMapsDistanceCalculator, distanceStore, c.EventBus)
	c.SolutionService = NewSolutionService(jobStore, vehicleStore, driverStore, locationStore, distanceStore, patternStore, solutionStore, allocationRunStore, c.SolverApi)
	c.AllocationRunService = NewAllocationRunService(allocationRunStore)

	c.MaterialCategoryCrudService = NewMaterialCategoryCrudService(materialCategoryStore)
	c.DriverCrudService = NewDriverCrudService(driverStore, materialCategoryStore)
	c.VehicleCrudService = NewVehicleCrudService(vehicleStore, materialCategoryStore)
	c.JobCrudService = NewJobCrudService(jobStore, processDetailsStore, materialCategoryStore, locationStore)

	c.PickupCrudService = NewPickupCrudService(pickupStore)
	c.CoordinatesUpdateService = NewCoordinatesUpdateService(pickupStore, destinationAreaStore, processDetailsStore, c.EventBus)
}

func (c *Container) configureEventBus() {
	c.EventBus = framework.NewEventBus()

	eventChannel := c.EventBus.Subscribe("DistanceMatrixUpdateToBeUpdated")
	go func() {
		for event := range eventChannel {
			c.LocationCrudService.HandleDistanceMatrixUpdateEvent(event)
		}
	}()
}

// openDB opens a database connection
func openDB(driver, connection string) (*sql.DB, error) {
	// Helper to automatically create the directories that the specified sqlite file
	// should reside in, if one
	if driver == "sqlite3" {
		d := strings.Split(connection, "/")

		if len(d) > 1 {
			path := strings.Join(d[:len(d)-1], "/")

			if err := os.MkdirAll(path, 0755); err != nil {
				return nil, err
			}
		}
	}

	return sql.Open(driver, connection)
}
