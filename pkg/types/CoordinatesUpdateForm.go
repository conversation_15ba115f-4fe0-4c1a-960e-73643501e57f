package types

import (
	"errors"
	"loms/framework/form"
)

type CoordinatesUpdateForm struct {
	ID         int64  `form:"id"`
	Type       int    `form:"type" validate:"min=0,max=1"`
	Name       string `form:"name"`
	RomsID     int64  `form:"romsId"`
	LocationID int64  `form:"locationId"`
	Zip        string `form:"zip"`
	PrefID     int64  `form:"prefID"`
	CityCD     string `form:"cityCd"`
	Add1       string `form:"add1"`
	Add1Kana   string `form:"add1_kana"`
	Add2       string `form:"add2"`
	Add2Kana   string `form:"add2_kana"`
	Add3       string `form:"add3"`
	Add3Kana   string `form:"add3_kana"`
	LatLng     string `form:"latLng" validate:"required"`

	IsEdit bool
	form.Submission
}

func ToCoordinatesFromPage(page *Page[PickupEntity]) *Page[*CoordinatesUpdateForm] {
	content := make([]*CoordinatesUpdateForm, len(page.Content))
	for i, loc := range page.Content {
		content[i] = FromDomainPickup(&loc)
	}
	return &Page[*CoordinatesUpdateForm]{Content: content, Total: page.Total, Paging: page.Paging}
}

func FromDomainPickup(entity *PickupEntity) *CoordinatesUpdateForm {
	locType := 0
	if entity.Location.Type == "m_dest_area" {
		locType = 1
	} else if entity.Location.Type == "m_pickup" {
		locType = 0
	} else {
		// not pickup/delivery
	}
	return &CoordinatesUpdateForm{
		ID:       entity.ID,
		Type:     locType,
		Name:     entity.Nm,
		RomsID:   entity.RomsID,
		Zip:      entity.Location.Zip,
		PrefID:   entity.Location.PrefId,
		CityCD:   entity.Location.CityCD,
		Add1:     entity.Location.Add1,
		Add1Kana: entity.Location.Add1Kana,
		Add2:     entity.Location.Add2,
		Add2Kana: entity.Location.Add2Kana,
		Add3:     entity.Location.Add3,
		Add3Kana: entity.Location.Add3Kana,
		LatLng:   entity.Location.LatLng,
	}
}

func (vm *CoordinatesUpdateForm) ToDomainPickup() (*PickupEntity, error) {
	if vm.ID == 0 && vm.IsEdit {
		return nil, errors.New("ID cannot be 0")
	}

	if vm.Name == "" {
		return nil, errors.New("name cannot be empty")
	}

	return &PickupEntity{
		ID: vm.ID,
		Nm: vm.Name,
		Location: &LocationEntity{
			ID: vm.LocationID,

			Add1:   vm.Add1,
			Add2:   vm.Add2,
			Add3:   vm.Add3,
			Zip:    vm.Zip,
			PrefId: vm.PrefID,
			CityCD: vm.CityCD,
			LatLng: vm.LatLng,
		},
	}, nil
}

func MapToPickupLocations(locations []CoordinatesUpdateForm) []PickupEntity {
	entities := make([]PickupEntity, len(locations))
	for i, loc := range locations {
		tmp := loc
		location, _ := tmp.ToDomainPickup()
		entities[i] = *location
	}
	return entities
}

func MapToDestinationEntities(locations []CoordinatesUpdateForm) []DestinationAreaEntity {
	entities := make([]DestinationAreaEntity, len(locations))
	for i, loc := range locations {
		entities[i] = DestinationAreaEntity{
			ID: loc.ID,
			Nm: loc.Name,
			Location: &LocationEntity{
				ID: loc.LocationID,

				Add1:   loc.Add1,
				Add2:   loc.Add2,
				Add3:   loc.Add3,
				Zip:    loc.Zip,
				PrefId: loc.PrefID,
				CityCD: loc.CityCD,
				LatLng: loc.LatLng,
			},
		}
	}
	return entities
}

func GetCoordinates(locationVMS []CoordinatesUpdateForm) []string {
	var coordinates []string
	for _, loc := range locationVMS {
		coordinates = append(coordinates, loc.LatLng)
	}
	return coordinates
}

type LocationImportInput struct {
	Message string `form:"message" validate:"required"`
}
