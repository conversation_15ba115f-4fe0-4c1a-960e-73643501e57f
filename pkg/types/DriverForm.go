package types

import (
	"errors"
	"fmt"
	"loms/framework/form"
	"strconv"
	"strings"

	"github.com/samber/lo"
)

type DriverForm struct {
	ID             int64   `form:"id"`
	RomsID         int64   `form:"romsId"`
	Name           string  `form:"name" validate:"required"`
	CanHandle      []int64 `form:"CanHandle" validate:"required"`
	CanHandleNames string
	LeaveDates     string `form:"LeaveDates"`

	IsEdit bool
	form.Submission
}

func ToDomainDriver(form *DriverForm) *DriverEntity {
	return &DriverEntity{
		ID:        form.ID,
		Nm1:       form.Name,
		CanHandle: form.CanHandle,
	}
}

func ToDriverFormPage(page *Page[DriverEntity]) *Page[*DriverForm] {
	content := make([]*DriverForm, len(page.Content))
	for i, loc := range page.Content {
		content[i] = FromDomainDriver(&loc)
	}
	return &Page[*DriverForm]{Content: content, Total: page.Total, Paging: page.Paging}
}

func FromDomainDriver(entity *DriverEntity) *DriverForm {
	return &DriverForm{
		ID:     entity.ID,
		RomsID: entity.RomsID,
		Name:   entity.Nm1,
		LeaveDates: lo.Reduce(entity.LeaveDates, func(acc string, ld DriverLeaveDateEntity, _ int) string {
			if acc == "" {
				return fmt.Sprintf("%d", ld.Date)
			}
			return acc + "," + fmt.Sprintf("%d", ld.Date)
		}, ""),
		CanHandle: lo.Map(entity.MaterialCategories, func(cat ItemEntity, _ int) int64 {
			return cat.ID
		}),
		CanHandleNames: lo.Reduce(entity.MaterialCategories, func(acc string, cat ItemEntity, _ int) string {
			if acc == "" {
				return cat.Name
			}
			return acc + ", " + cat.Name
		}, ""),
	}
}

func (vm *DriverForm) ToDomainDriver() (*DriverEntity, error) {
	if vm.ID == 0 && vm.IsEdit {
		return nil, errors.New("ID cannot be 0")
	}

	if vm.Name == "" {
		return nil, errors.New("name cannot be empty")
	}

	dates := lo.Map(strings.Split(vm.LeaveDates, ","), func(date string, _ int) int64 {
		dateInt, err := strconv.ParseInt(date, 10, 64)
		if err != nil {
			return 0
		}
		return dateInt
	})
	dates = lo.Uniq(dates)

	entity := &DriverEntity{
		ID:              vm.ID,
		Nm1:             vm.Name,
		CanHandle:       vm.CanHandle,
		LeaveTakenDates: dates,
	}
	return entity, nil
}
