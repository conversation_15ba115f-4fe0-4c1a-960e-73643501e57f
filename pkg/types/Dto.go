package types

import "time"

type (
	AllocationSaveDto struct {
		CreatedAt   int64
		Status      int
		FulfillDate time.Time
	}

	AllocationSaveInput struct {
		Status             int   `json:"status" validate:"required"`
		FulfillDate        int64 `json:"fulfillDate" validate:"required"`
		WithHistory        bool  `json:"withHistory"`
		WithEvenAllocation bool  `json:"withEvenAllocation"`
	}

	AllocationUpdateInput struct {
		ID          int64 `json:"id" validate:"required"`
		Status      int   `json:"status" validate:"required"`
		FulfillDate int64 `json:"fulfillDate" validate:"required"`
	}

	SolutionSaveInput struct {
		ID              int64             `json:"id"`
		RunID           int64             `json:"runId" validate:"required"`
		TotalTime       int64             `json:"totalTime" validate:"required"`
		SolutionDetails []SolutionDetails `json:"solutionDetails" validate:"required"`
		Timeline        []TimelineTask    `json:"timeline" validate:"required"`
		Analysis        SolutionAnalysis  `json:"analysis" validate:"required"`
		Score           string            `json:"score" validate:"required"`
		Feasible        int               `json:"feasible"`
		Manual          bool              `json:"manual"`
		OldSolutionId   int64             `json:"oldSolutionId"`
	}

	SolutionSaveOutput struct {
		ID int64 `json:"id"`
	}

	SolutionDetails struct {
		VehicleID     int64  `json:"vehicleId" validate:"required"`
		DriverID      int64  `json:"driverId" validate:"required"`
		OrderID       int64  `json:"orderId" validate:"required"`
		VisitIndex    int    `json:"visitIndex" validate:"required"`
		UniqueID      string `json:"uniqueId" validate:"required"`
		ArrivalTime   int64  `json:"arrivalTime" validate:"required"`
		ServiceTime   int64  `json:"serviceTime" validate:"required"`
		DepartureTime int64  `json:"departureTime" validate:"required"`
		TimeToDepot   int64  `json:"timeToDepot"`
	}

	SolutionAnalysis struct {
		Constraints []SolutionConstraint `json:"constraints" validate:"required"`
	}

	SolutionConstraint struct {
		Package    string `json:"package" validate:"required"`
		Name       string `json:"name" validate:"required"`
		Weight     string `json:"weight" validate:"required"`
		Score      string `json:"score" validate:"required"`
		MatchCount int    `json:"matchCount" validate:"required"`
	}
)

type TaskType string

const (
	TaskDepotStart TaskType = "DepotStart"
	TaskPickup     TaskType = "Pickup"
	TaskDelivery   TaskType = "Delivery"
	TaskCleaning   TaskType = "Cleaning"
	TaskLunch      TaskType = "Lunch"
	TaskWaiting    TaskType = "Waiting"
	TaskDriving    TaskType = "Driving"
	TaskDepotEnd   TaskType = "DepotEnd"
)

// Location model
type Location struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Address   string  `json:"address"`
}

// TimelineTask model
type TimelineTask struct {
	VehicleID            int64    `json:"vehicleId"`
	DriverID             int64    `json:"driverId"`
	OrderID              int64    `json:"orderId"`
	TaskIndex            int      `json:"taskIndex"`
	TaskType             TaskType `json:"taskType"`
	StartTime            int64    `json:"startTime"`
	EndTime              int64    `json:"endTime"`
	Duration             int64    `json:"duration"`
	LocationUniqueID     string   `json:"locationUniqueId"`
	ConsolidatedOrderIDs string   `json:"consolidatedOrderIds"`
	FromLocationUniqueID string   `json:"fromLocationUniqueId"`
	ToLocationUniqueID   string   `json:"toLocationUniqueId"`
	TravelTime           int64    `json:"travelTime"`
	IsReturnToDepot      bool     `json:"isReturnToDepot"`
	Reason               string   `json:"reason"`
}
