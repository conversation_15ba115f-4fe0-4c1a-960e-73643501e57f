package types

import (
	"math"
	"strings"
)

type PageRequest struct {
	Sort  string `query:"sort"`
	Order string `query:"order"`
	Page  int    `query:"page"`
	Size  int    `query:"size"`
}

func NewPageRequestOf(request *PageRequest) *PageRequest {
	sortVal := "Id"
	orderVal := "asc"
	pageVal := 1
	sizeVal := 20
	if request.Sort != "" {
		sortVal = request.Sort
	}
	if request.Order != "" {
		orderVal = request.Order
	}
	if request.Page != 0 {
		pageVal = request.Page
	}
	if request.Size != 0 {
		sizeVal = request.Size
	}
	return &PageRequest{sortVal, orderVal, pageVal, sizeVal}
}

func UnPaged() *PageRequest {
	return NewPageRequestOf(&PageRequest{})
}

type Page[T any] struct {
	Content []T
	Total   int
	Paging  *PageRequest
}

func NewPage[T any](content []T, total int, paging *PageRequest) Page[T] {
	return Page[T]{content, total, paging}
}

func (p Page[T]) Iterator() <-chan T {
	ch := make(chan T)
	go func() {
		for _, item := range p.Content {
			ch <- item
		}
		close(ch)
	}()
	return ch
}

func (p Page[T]) TotalPages() int {
	if p.Paging.Size == 0 {
		return 1
	}
	return int(math.Ceil(float64(p.Total) / float64(p.Paging.Size)))
}

func (p Page[T]) Page() int {
	return p.Paging.Page
}

func (p Page[T]) Size() int {
	return p.Paging.Size
}

func (p Page[T]) Sort() string {
	return p.Paging.Sort
}

func (p Page[T]) Order() string {
	return p.Paging.Order
}

func (p Page[T]) PreviousPage() int {
	return p.Paging.PreviousPage()
}

func (p Page[T]) NextPage() int {
	return p.Paging.NextPage(p.TotalPages())
}

func (p Page[T]) Numbers() []int {
	return p.Paging.Numbers(p.TotalPages())
}

func (p Page[T]) HasMore() bool {
	return p.Paging.HasMore(p.TotalPages())
}

func (p *PageRequest) NextOrder(currentColumn string) string {
	if p.Sort == "" || !strings.EqualFold(currentColumn, p.Sort) {
		return "asc"
	} else if strings.EqualFold("desc", p.Order) {
		return "asc"
	} else {
		return "desc"
	}
}

func (p *PageRequest) GetCssClass(column string) string {
	if p.Sort == "" || !strings.EqualFold(column, p.Sort) {
		return ""
	} else if strings.EqualFold("desc", p.NextOrder(column)) {
		return "sort-up-alt"
	} else {
		return "sort-down-alt"
	}
}

func (p *PageRequest) WithPage(page int) *PageRequest {
	p.Page = page
	return p
}

func (p *PageRequest) PreviousPage() int {
	if p.Page < 2 {
		return 1
	}
	return p.Page - 1
}

func (p *PageRequest) NextPage(totalPages int) int {
	if p.Page < totalPages {
		return p.Page + 1
	}
	return totalPages
}

func (p *PageRequest) Numbers(totalPages int) []int {
	end := totalPages
	if end > 5+p.Page {
		end = 5 + p.Page
	}
	numbers := make([]int, end)
	for i := 1; i <= end; i++ {
		numbers[i-1] = i
	}
	return numbers
}

func (p *PageRequest) HasMore(totalPages int) bool {
	return p.Page+5 < totalPages
}
