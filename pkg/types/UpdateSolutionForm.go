package types

import "loms/framework/form"

type (
	UpdateSolutionForm struct {
		SolutionID    int64               `json:"solutionId" validate:"gt=0"`
		JobID         int64               `json:"jobId" validate:"gt=0"`
		FromVehicleID int64               `json:"fromVehicleId" validate:"gt=0"`
		FromDriverID  int64               `json:"fromDriverId" validate:"gt=0"`
		ToDriverID    int64               `json:"toDriverId" validate:"gt=0"`
		ToVehicleID   int64               `json:"toVehicleId" validate:"gt=0"`
		GroupChanged  bool                `json:"groupChanged" validate:"boolean"`
		Changes       []JobSequenceChange `json:"changes" validate:"required,dive"`

		form.Submission
	}
	JobSequenceChange struct {
		OldIndex   int64 `json:"oldIndex" validate:"gte=0"`
		NewIndex   int64 `json:"newIndex" validate:"gte=0"`
		JobID      int64 `json:"jobId" validate:"gt=0"`
		SolutionID int64 `json:"solutionId" validate:"gt=0"`
		DriverID   int64 `json:"driverId" validate:"gt=0"`
		VehicleID  int64 `json:"vehicleId" validate:"gt=0"`
		Changed    bool  `json:"changed" validate:"boolean"`
	}
)
