package types

import (
	"loms/framework/form"
)

type (
	ForgotPasswordForm struct {
		Email string `form:"email" validate:"required,email"`
		form.Submission
	}

	LoginForm struct {
		Email    string `form:"email" validate:"required,email"`
		Password string `form:"password" validate:"required"`
		form.Submission
	}

	RegisterForm struct {
		Name            string `form:"name" validate:"required"`
		Email           string `form:"email" validate:"required,email"`
		Password        string `form:"password" validate:"required"`
		ConfirmPassword string `form:"ConfirmPassword" validate:"required,eqfield=Password"`
		form.Submission
	}

	ResetPasswordForm struct {
		Password        string `form:"password" validate:"required"`
		ConfirmPassword string `form:"password-confirm" validate:"required,eqfield=Password"`
		form.Submission
	}
)
