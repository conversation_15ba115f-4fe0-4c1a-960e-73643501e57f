package types

type (
	DistanceResponse struct {
		OriginAddresses      []string      `json:"origin_addresses"`
		DestinationAddresses []string      `json:"destination_addresses"`
		Rows                 []DistanceRow `json:"rows"`
	}

	DistanceRow struct {
		Elements []DistanceElement `json:"elements"`
	}

	DistanceElement struct {
		Distance *DistanceDetails `json:"distance,omitempty"`
		Duration *DistanceDetails `json:"duration,omitempty"`
		Status   string           `json:"status"`
	}

	DistanceDetails struct {
		Text  string `json:"text"`
		Value int64  `json:"value"`
	}
)
