package types

type TemplCtxKey string

const (
	TemplCtxKeyPage  TemplCtxKey = "page"
	TemplCtxKeyFuncs TemplCtxKey = "funcs"

	ApplicationDbDateFormat     = "2006-01-02"
	ApplicationDbDateTimeFormat = "2006-01-02T15:04:05"

	RouteForgotPassword       = "forgot_password"
	RouteForgotPasswordSubmit = "forgot_password.submit"
	RouteLogin                = "login"
	RouteLoginSubmit          = "login.submit"
	RouteLogout               = "logout"
	RouteRegister             = "register"
	RouteRegisterSubmit       = "register.submit"
	RouteResetPassword        = "reset_password"
	RouteResetPasswordSubmit  = "reset_password.submit"
	RouteVerifyEmail          = "verify_email"

	RouteHome = "home"

	RouteSolution        = "solution"
	RouteSolutionDetails = "solution_details"
	RouteRunDetails      = "solution_run"
	RouteMap             = "solution_map"
	RouteSolve           = "solution_solve"
	UpdateSolution       = "update_solution"
	RouteSolutionDelete  = "solution_delete"

	RouteLocations          = "locations"
	RouteLocationAddNew     = "location_add"
	RouteLocationDetails    = "location_details"
	RouteLocationUpdate     = "location_update"
	RouteLocationImport     = "location_import"
	RouteLocationImportPost = "location_import_post"

	RouteCategories        = "categories"
	RouteCategoriesAddNew  = "categories_add"
	RouteCategoriesDetails = "categories_details"
	RouteCategoriesDelete  = "categories_delete"
	RouteCategoriesUpdate  = "categories_update"

	RouteDrivers        = "drivers"
	RouteDriversAddNew  = "driver_add"
	RouteDriversDetails = "driver_details"
	RouteDriversUpdate  = "driver_update"

	RoutePickups                     = "pickups"
	RoutePickupsAddNew               = "pickup_add"
	RoutePickupsDetails              = "pickup_details"
	RoutePickupsUpdate               = "pickup_update"
	RouteCoordinatesUpdateImport     = "coordinates_update_import"
	RouteCoordinatesUpdateImportPost = "coordinates_update_import_post"

	RouteVehicles        = "vehicles"
	RouteVehiclesAddNew  = "vehicle_add"
	RouteVehiclesDetails = "vehicle_details"
	RouteVehiclesUpdate  = "vehicle_update"

	RouteJobs       = "orders"
	RouteJobsByDate = "orders_by_date"
	RouteJobAddNew  = "job_add"
	RouteJobDetails = "job_details"
	RouteJobUpdate  = "job_update"
	RouteJobImport  = "job_import"
)
