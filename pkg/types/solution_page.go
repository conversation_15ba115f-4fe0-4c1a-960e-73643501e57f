package types

import "fmt"

type (
	SolutionIndexInput struct {
		Date       string `query:"Date"`
		RunId      int64  `query:"RunId"`
		SolutionId int64  `query:"SolutionId"`
	}

	JobIndexData struct {
		Date string `query:"Date"`
	}

	SolutionMapInput struct {
		ApiUrl         string
		ToBeLoadedFrom string
		CustomerId     string
		Signature      string
		RequestCode    string
	}

	SolutionIndexData struct {
		Date       string
		RunId      int64
		SolutionId int64
		Runs       []RunDropDownData
		Solutions  []SolutionDropDownData
	}

	RunDropDownData struct {
		RunID int64
		Text  string
	}

	SolutionDropDownData struct {
		ID           int64
		TotalTime    int64
		Score        string
		TimeToCreate int64
		IsManual     bool
	}
	SolutionRunData struct {
		Date             string
		SolverInProgress bool
		Title            string
	}

	SolutionDetailsData struct {
		Solution      *SolutionVM
		ErrorMessages []string
		Vehicle       int64
		IsInclude     bool
	}
)

func (data *SolutionMapInput) GetMapUrl() string {
	return fmt.Sprintf("%s/%s/v2/map_script?host=%s&signature=%s&request_code=%s",
		data.ApiUrl,
		data.CustomerId,
		data.ToBeLoadedFrom,
		data.Signature,
		data.RequestCode,
	)
}
