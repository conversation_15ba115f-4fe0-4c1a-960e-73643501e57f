package types

import (
	"errors"
	"loms/framework/form"
)

type MaterialCategoryForm struct {
	ID     int64  `form:"id"`
	Name   string `form:"name" validate:"required"`
	RomsID int64  `form:"romsId" validate:"required"`

	IsEdit bool
	form.Submission
}

func ToMaterialCategoryFormPage(page *Page[ItemEntity]) *Page[*MaterialCategoryForm] {
	content := make([]*MaterialCategoryForm, len(page.Content))
	for i, loc := range page.Content {
		content[i] = FromDomainMaterialCategory(&loc)
	}
	return &Page[*MaterialCategoryForm]{Content: content, Total: page.Total, Paging: page.Paging}
}

func FromDomainMaterialCategory(entity *ItemEntity) *MaterialCategoryForm {
	return &MaterialCategoryForm{
		ID:     entity.ID,
		Name:   entity.Name,
		RomsID: entity.RomsID,
	}
}

func (vm *MaterialCategoryForm) ToDomainMaterialCategory() (*ItemEntity, error) {
	if vm.ID == 0 && vm.IsEdit {
		return nil, errors.New("ID cannot be 0")
	}

	if vm.Name == "" {
		return nil, errors.New("name cannot be empty")
	}

	return &ItemEntity{
		ID:     vm.ID,
		Name:   vm.Name,
		RomsID: vm.RomsID,
	}, nil
}
