package types

import (
	"errors"
	"loms/framework/form"

	"github.com/samber/lo"
)

type VehicleForm struct {
	ID             int64   `form:"id"`
	Name           string  `form:"name" validate:"required"`
	Color          string  `form:"color" validate:"required"`
	Capacity       int64   `form:"Capacity" validate:"required"`
	CapacityLiters int64   `form:"CapacityLiters" validate:"required"`
	CanHandle      []int64 `form:"CanHandle" validate:"required"`
	CanHandleNames string

	IsEdit bool
	form.Submission
}

func ToDomainVehicle(form *VehicleForm) *VehicleEntity {
	return &VehicleEntity{
		ID:        form.ID,
		SerialNm:  form.Name,
		CanHandle: form.CanHandle,
	}
}

func ToVehicleFormPage(page *Page[VehicleEntity]) *Page[*VehicleForm] {
	content := make([]*VehicleForm, len(page.Content))
	for i, loc := range page.Content {
		content[i] = FromDomainVehicle(&loc)
	}
	return &Page[*VehicleForm]{Content: content, Total: page.Total, Paging: page.Paging}
}

func FromDomainVehicle(entity *VehicleEntity) *VehicleForm {
	return &VehicleForm{
		ID:   entity.ID,
		Name: entity.SerialNm,
		/*Color:          entity.Color,
		Capacity:       entity.Capacity,
		CapacityLiters: entity.CapacityLiters,*/

		CanHandle: lo.Map(entity.MaterialCategories, func(cat ItemEntity, _ int) int64 {
			return cat.ID
		}),
		CanHandleNames: lo.Reduce(entity.MaterialCategories, func(acc string, cat ItemEntity, _ int) string {
			if acc == "" {
				return cat.Name
			}
			return acc + ", " + cat.Name
		}, ""),
	}
}

func (vm *VehicleForm) ToDomainVehicle() (*VehicleEntity, error) {
	if vm.ID == 0 && vm.IsEdit {
		return nil, errors.New("ID cannot be 0")
	}

	if vm.Name == "" {
		return nil, errors.New("name cannot be empty")
	}

	return &VehicleEntity{
		ID:        vm.ID,
		SerialNm:  vm.Name,
		CanHandle: vm.CanHandle,
	}, nil
}
