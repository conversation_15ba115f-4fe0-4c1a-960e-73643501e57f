package types

import (
	"fmt"
	"time"
)

type (
	SolutionVM struct {
		Date            string
		DeliveryDate    time.Time
		RunId           int64
		SolutionId      int64
		ManuallyChanged bool
		TotalTime       int64
		SolverStatus    string
		Data            string
		Constraints     []SolutionConstraintVM
		VehicleRoutes   []VehicleRouteVM
		Vehicles        []VehicleVM
	}

	SolutionConstraintVM struct {
		Name       string `json:"name" binding:"required"`
		Weight     string `json:"weight" binding:"required"`
		Score      string `json:"score" binding:"required"`
		IsViolated bool   `json:"isViolated" binding:"required"`
		MatchCount int    `json:"matchCount" binding:"required"`
	}

	VehicleRouteVM struct {
		Vehicle VehicleVM      `json:"vehicle" binding:"required"`
		Driver  DriverVM       `json:"driver" binding:"required"`
		Visits  []TimelineVM   `json:"visits" binding:"required"`
		Jobs    []JobDisplayVM `json:"jobs" binding:"required"`
		Track   []Coordinates  `json:"track" binding:"required"`
	}

	JobDisplayVM struct {
		RomsJobID string  `json:"romsJobId" binding:"required"`
		JobID     int64   `json:"jobId" binding:"required"`
		JobIndex  int     `json:"jobIndex" binding:"required"`
		Material  string  `json:"material" binding:"required"`
		Pickup    VisitVM `json:"pickup" binding:"required"`
		Delivery  VisitVM `json:"delivery" binding:"required"`
		SetTime   string  `json:"setTime" binding:"required"`
	}

	DriverVM struct {
		ID   int64  `json:"id" binding:"required"`
		Name string `json:"name" binding:"required"`
	}

	VehicleVM struct {
		ID            int64    `json:"id" binding:"required"`
		RomsID        string   `json:"romsId" binding:"required"`
		Name          string   `json:"name" binding:"required"`
		Color         string   `json:"color" binding:"required"`
		CanHandle     string   `json:"canHandle" binding:"required"`
		CanHandleList []string `json:"canHandleList" binding:"required"`
		Time          int64    `json:"time" binding:"required"`

		Driver DriverVM `json:"driver" binding:"required"`
	}

	VisitVM struct {
		Name            string  `json:"name" binding:"required"`
		Address         string  `json:"address" binding:"required"`
		JobID           int64   `json:"jobId" binding:"required"`
		RomsJobID       string  `json:"romsJobId" binding:"required"`
		Type            string  `json:"type" binding:"required"`
		Lat             float64 `json:"lat" binding:"required"`
		Lon             float64 `json:"lon" binding:"required"`
		Category        string  `json:"category" binding:"required"`
		ArrivalTime     int64   `json:"arrivalTime" binding:"required"`
		ServiceDuration int64   `json:"serviceDuration" binding:"required"`
		DepartureTime   int64   `json:"departureTime" binding:"required"`
		DistanceToDepot int64   `json:"distanceToDepot"`
	}

	TimelineVM struct {
		Visit VisitVM `json:"visit" binding:"required"`

		VehicleID            int64  `json:"vehicleId" binding:"required"`
		DriverID             int64  `json:"driverId" binding:"required"`
		OrderID              int64  `json:"orderId" binding:"required"`
		RomsJobID            string `json:"romsJobId" binding:"required"`
		TaskIndex            int    `json:"taskIndex" binding:"required"`
		TaskType             string `json:"taskType" binding:"required"`
		TaskTypeJapanese     string `json:"taskTypeJapanese" binding:"required"`
		StartTime            int64  `json:"startTime" binding:"required"`
		EndTime              int64  `json:"endTime" binding:"required"`
		Duration             int64  `json:"duration" binding:"required"`
		LocationUniqueID     string `json:"locationUniqueId" binding:"required"`
		ConsolidatedOrderIDs string `json:"consolidatedOrderIds" binding:"required"`
		FromLocationUniqueID string `json:"fromLocationUniqueId" binding:"required"`
		ToLocationUniqueID   string `json:"toLocationUniqueId" binding:"required"`
		Reason               string `json:"reason" binding:"required"`
	}

	Coordinates struct {
		Lat float64 `json:"lat"`
		Lon float64 `json:"lon"`
	}

	JobVM struct {
		ID               int64
		PickupLocation   string
		DeliveryLocation string
		MaterialCategory string
	}
)

func (c Coordinates) String() string {
	return fmt.Sprintf("%f, %f", c.Lat, c.Lon)
}
