package types

type (
	SolutionChangeValidationRequest struct {
		RunID        int64               `json:"runId" validate:"gt=0"`
		SolutionID   int64               `json:"solutionId" validate:"gt=0"`
		Changes      []JobSequenceChange `json:"changes" validate:"required,dive"`
		FromProblem  *Problem            `json:"fromProblem" validate:"required,dive"`
		ToProblem    *Problem            `json:"toProblem" validate:"required,dive"`
		GroupChanged bool                `json:"groupChanged" validate:"boolean"`
	}

	SolutionChangeValidationResponse struct {
		IsValid  bool              `json:"valid"`
		Messages []string          `json:"messages"`
		Solution SolutionSaveInput `json:"solution"`
	}

	Problem struct {
		Jobs               []ProblemJob      `json:"orders"`
		Items              []ProblemItem     `json:"items"`
		Locations          []ProblemLocation `json:"locations"`
		Distances          []ProblemDistance `json:"distances"`
		Vehicles           []ProblemVehicle  `json:"vehicles"`
		Drivers            []ProblemDriver   `json:"drivers"`
		Patterns           []ProblemPattern  `json:"patterns"`
		WithHistory        bool              `json:"withHistory"`
		WithEvenAllocation bool              `json:"withEvenAllocation"`
	}

	ProblemJob struct {
		ID                 int64 `json:"id"`
		PickupLocationID   int64 `json:"pickupId"`
		DeliveryLocationID int64 `json:"deliveryId"`
		ItemID             int64 `json:"itemId"`
		ShipperID          int64 `json:"shipperId"`
		FulfillDate        int64 `json:"fulfillDate"`
		Weight             int32 `json:"weight"`
		Litres             int32 `json:"litres"`
	}

	ProblemLocation struct {
		ID              int64   `json:"id"`
		UniqueID        string  `json:"uniqueId"`
		LocationType    int     `json:"locationType"`
		Description     string  `json:"description"`
		Address         string  `json:"address"`
		Lat             float64 `json:"lat"`
		Lon             float64 `json:"lon"`
		IsDepot         bool    `json:"depot"`
		ReadyTime       int64   `json:"readyTime"`
		DueTime         int64   `json:"dueTime"`
		ServiceDuration int64   `json:"serviceDuration"`
	}

	ProblemVehicle struct {
		ID                int64   `json:"id"`
		Name              string  `json:"name"`
		Color             string  `json:"color"`
		Capacity          int64   `json:"capacity"`
		CapacityLiters    int64   `json:"capacityLiters"`
		WeightCapacity    int32   `json:"weightCapacity"`
		TotalTankCapacity int32   `json:"totalTankCapacity"`
		FrontTankCapacity int32   `json:"frontTankCapacity"`
		RearTankCapacity  int32   `json:"rearTankCapacity"`
		CanHandle         []int64 `json:"canHandle"`
	}

	ProblemItem struct {
		ID           int64  `json:"id"`
		Name         string `json:"name"`
		CleaningFlag bool   `json:"cleaningFlag"`
	}

	ProblemDistance struct {
		FromLoc  int64 `json:"fromLoc"`
		ToLoc    int64 `json:"toLoc"`
		Distance int64 `json:"distance"`
	}

	ProblemDriver struct {
		ID             int64   `json:"id"`
		Name           string  `json:"name"`
		CanHandle      []int64 `json:"canHandle"`
		CanDrive       []int64 `json:"canDrive"`
		PrimaryVehicle int64   `json:"primaryVehicle"`
	}

	ProblemPattern struct {
		ShipperID          int64 `json:"shipperId"`
		PickupLocationID   int64 `json:"pickupId"`
		DeliveryLocationID int64 `json:"deliveryId"`
		ItemID             int64 `json:"itemId"`
		VehicleID          int64 `json:"vehicleId"`
		DriverID           int64 `json:"driverId"`
	}
)
