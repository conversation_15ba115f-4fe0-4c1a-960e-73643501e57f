package types

import (
	"loms/framework/form"
)

type JobForm struct {
	ID                 int64  `form:"id"`
	PickupID           int64  `form:"pickupID" validate:"required" validate:"gt=0"`
	DestCD             string `form:"destCD" validate:"required" validate:"gt=0"`
	MaterialCategoryID int64  `form:"materialCategoryID" validate:"required" validate:"gt=0"`
	FulfillDate        string `form:"fulfillDate" validate:"required"`

	Pickup           string
	Delivery         string
	MaterialCategory string

	IsEdit bool
	form.Submission
}

func ToJobFormPage(page *Page[JobEntity]) *Page[*JobForm] {
	content := make([]*JobForm, len(page.Content))
	for i, job := range page.Content {
		content[i] = FromDomainJob(&job)
	}
	return &Page[*JobForm]{Content: content, Total: page.Total, Paging: page.Paging}
}

func (vm *JobForm) ToDomainJob() (*JobEntity, error) {
	ymd, _ := DateStrToYmd(vm.FulfillDate)
	return &JobEntity{
		ID:       vm.ID,
		PickupID: vm.PickupID,
		DestCD:   vm.DestCD,
		ItemID:   vm.MaterialCategoryID,
		ShipDate: ymd,
	}, nil
}

func FromDomainJob(entity *JobEntity) *JobForm {
	return &JobForm{
		ID:                 entity.ID,
		PickupID:           entity.PickupID,
		DestCD:             entity.DestCD,
		MaterialCategoryID: entity.ItemID,
		FulfillDate:        DateYmdToDateStr(entity.ShipDate),

		Pickup:           entity.Pickup.Nm,
		Delivery:         entity.DestinationGroup.DestCD,
		MaterialCategory: entity.Item.Name,
	}
}
