package types

import (
	"strconv"
	"strings"
)

func DateYmdToDateStr(date int64) string {
	dateStr := strconv.FormatInt(date, 10)

	// Extract year, month, and day
	year := dateStr[:4]   // First 4 characters for the year
	month := dateStr[4:6] // Next 2 characters for the month
	day := dateStr[6:]    // Last 2 characters for the day

	// Format the date as "YYYY-MM-DD"
	formattedDate := year + "-" + month + "-" + day
	return formattedDate
}

func DateStrToYmd(dateStr string) (int64, error) {
	// Remove hyphens from the string (YYYY-MM-DD -> YYYYMMDD)
	normalizedDate := strings.Replace(dateStr, "-", "", -1)

	// Convert the resulting string to int64
	dateInt64, err := strconv.ParseInt(normalizedDate, 10, 64)
	if err != nil {
		return 0, err // Return 0 and the error if parsing fails
	}

	return dateInt64, nil
}
