package types

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/uptrace/bun"
)

type (
	User struct {
		bun.BaseModel `bun:"table:m_users,alias:u"`

		ID        int64 `bun:",pk,autoincrement"`
		Name      string
		Email     string
		Password  string
		Verified  bool
		IsAdmin   bool
		Status    string
		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`
	}

	PasswordToken struct {
		bun.BaseModel `bun:"table:password_tokens,alias:pk"`

		ID                int64
		Hash              string
		CreatedAt         time.Time
		PasswordTokenUser int64
	}

	DistanceEntity struct {
		bun.BaseModel `bun:"table:m_distances,alias:dis"`

		FromLocId int64
		ToLocId   int64
		Duration  int64

		From *LocationEntity `bun:"rel:belongs-to,join:from_loc_id=id"`
		To   *LocationEntity `bun:"rel:belongs-to,join:to_loc_id=id"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`
	}

	PatternEntity struct {
		bun.BaseModel `bun:"table:m_patterns"`

		ShipperID  int64
		PickupID   int64
		DeliveryID int64
		ItemID     int64
		VehicleID  int64
		DriverID   int64

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`
	}

	SolutionEntity struct {
		bun.BaseModel `bun:"table:t_solutions,alias:s"`

		ID         int64 `bun:",pk,autoincrement"`
		RunID      int64
		TotalTime  int64
		Score      string
		IsFeasible int
		IsManual   bool
		CreatedAt  int64
		CreatedBy  int64
		UpdatedAt  int64
		UpdatedBy  int64
		DeletedAt  time.Time `bun:",soft_delete,nullzero"`

		Run *AllocationRunEntity `bun:"rel:belongs-to,join:run_id=id"`
		//SolutionDetails []*SolutionDetailsEntity    `bun:"rel:has-many,join:id=solution_id"`
		Timeline    []*SolutionTimelineEntity   `bun:"rel:has-many,join:id=solution_id"`
		Constraints []*SolutionConstraintEntity `bun:"rel:has-many,join:id=solution_id"`
	}

	SolutionConstraintEntity struct {
		bun.BaseModel `bun:"table:t_solution_constraints,alias:sc"`

		ID         int64 `bun:",pk,autoincrement"`
		SolutionID int64
		Package    string
		Name       string
		Weight     string
		Score      string
		MatchCount int
	}

	SolutionDetailsEntity struct {
		bun.BaseModel `bun:"table:t_solution_details,alias:sd"`

		ID               int64 `bun:",pk,autoincrement"`
		SolutionID       int64
		VehicleID        int64
		DriverID         int64
		OrderID          int64
		VisitIndex       int
		LocationUniqueID string
		ArrivalTime      int64
		ServiceTime      int64
		DepartureTime    int64
		TimeToDepot      int64

		Location *LocationEntity `bun:"rel:belongs-to,join:location_unique_id=unique_id" json:"location"`
		Vehicle  *VehicleEntity  `bun:"rel:belongs-to,join:vehicle_id=id" json:"vehicle"`
		Driver   *DriverEntity   `bun:"rel:belongs-to,join:driver_id=id" json:"driver"`
		Order    *JobEntity      `bun:"rel:belongs-to,join:order_id=id" json:"order"`
	}

	SolutionTimelineEntity struct {
		bun.BaseModel `bun:"table:t_solution_timeline,alias:st"`

		ID                   int64 `bun:",pk,autoincrement"`
		SolutionID           int64
		VehicleID            int64
		DriverID             int64
		OrderID              int64
		TaskIndex            int
		TaskType             string
		StartTime            int64
		EndTime              int64
		Duration             int64
		LocationUniqueID     string
		ConsolidatedOrderIDs string `bun:"consolidated_order_ids"`
		FromLocationUniqueID string
		ToLocationUniqueID   string
		Reason               string

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		Vehicle  *VehicleEntity  `bun:"rel:belongs-to,join:vehicle_id=id" json:"vehicle"`
		Driver   *DriverEntity   `bun:"rel:belongs-to,join:driver_id=id" json:"driver"`
		Order    *JobEntity      `bun:"rel:belongs-to,join:order_id=id" json:"order"`
		Location *LocationEntity `bun:"rel:belongs-to,join:location_unique_id=unique_id" json:"location"`
	}

	AllocationRunEntity struct {
		bun.BaseModel `bun:"table:t_allocation_runs,alias:a"`

		ID                 int64 `bun:",pk,autoincrement" json:"id"`
		Status             int   `json:"status"`
		FulfillDate        int64 `json:"fulfillDate"`
		WithHistory        bool  `json:"withHistory"`
		WithEvenAllocation bool  `json:"withEvenAllocation"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		Solutions []*SolutionEntity `bun:"rel:has-many,join:id=run_id"`
	}

	PickupEntity struct {
		bun.BaseModel `bun:"table:m_pickups,alias:p"`

		ID         int64  `bun:",pk,autoincrement" json:"id"`
		Nm         string `json:"nm"`
		Kana       string `json:"kana"`
		Snm1       string `json:"snm1"`
		Snm2       string `json:"snm2"`
		LocationId int64  `json:"locationId"`
		StartTime  string `json:"startTime"`
		LunchTime  string `json:"lunchTime"`
		EndTime    string `json:"endTime"`
		Opt1       string `json:"opt1"`
		Opt2       string `json:"opt2"`
		Opt3       string `json:"opt3"`
		RomsID     int64  `json:"romsId"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		Location *LocationEntity `bun:"rel:belongs-to,join:location_id=id" json:"location"`
	}

	LocationEntity struct {
		bun.BaseModel `bun:"table:m_locations,alias:l"`

		ID       int64  `bun:",pk,autoincrement" json:"id"`
		LatLng   string `json:"latLng"`
		Zip      string `json:"zip"`
		PrefId   int64  `json:"prefId"`
		CityCD   string `json:"cityCd"`
		Add1     string `json:"add1"`
		Add1Kana string `json:"add1Kana"`
		Add2     string `json:"add2"`
		Add2Kana string `json:"add2Kana"`
		Add3     string `json:"add3"`
		Add3Kana string `json:"add3Kana"`
		Tel      string `json:"tel"`
		Fax      string `json:"fax"`
		UniqueID string `json:"uniqueId"`
		Type     string
		TypeID   int64

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		// Pref *Pref `bun:"rel:belongs-to,join:location_id=id" json:"location"`
	}

	GroupEntity struct {
		bun.BaseModel `bun:"table:m_group,alias:g"`

		ID         int64  `bun:",pk,autoincrement" json:"id"`
		Nm         string `json:"nm"`
		GroupNm    string `json:"groupNm"`
		GroupKana  string `json:"groupKana"`
		GroupType  int    `json:"groupType"`
		ItemCat    string `json:"itemCat"`
		LocationID int64  `json:"locationId"`
		Opt1       string `json:"opt1"`
		Opt2       string `json:"opt2"`
		RomsID     int64  `json:"romsId"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		Location *LocationEntity `bun:"rel:belongs-to,join:location_id=id" json:"location"`
	}

	DestinationCompanyEntity struct {
		bun.BaseModel `bun:"table:m_dest_company,alias:dc"`

		ID     int64  `bun:",pk,autoincrement" json:"id"`
		Name   string `json:"name"`
		Kana   string `json:"kana"`
		Opt1   string `json:"opt1"`
		Opt2   string `json:"opt2"`
		Opt3   string `json:"opt3"`
		RomsID int64  `json:"romsId"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`
	}

	DestinationAreaEntity struct {
		bun.BaseModel `bun:"table:m_dest_area,alias:da"`

		ID                   int64  `bun:",pk,autoincrement" json:"id"`
		DestinationCompanyId int64  `json:"destinationCompanyId" bun:"dest_company_id"`
		SerialNo             int64  `json:"serialNo"`
		Nm                   string `json:"nm"`
		Kana                 string `json:"kana"`
		Opt1                 string `json:"opt1"`
		Opt2                 string `json:"opt2"`
		Opt3                 string `json:"opt3"`
		LocationID           int64  `json:"locationId"`
		RomsID               int64  `json:"romsId"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		DestinationCompany *DestinationCompanyEntity `bun:"rel:belongs-to,join:dest_company_id=id" json:"destinationCompany"`
		Location           *LocationEntity           `bun:"rel:belongs-to,join:location_id=id" json:"location"`
	}

	DestinationSpotEntity struct {
		bun.BaseModel `bun:"table:m_dest_spot,alias:ds"`

		ID                int64  `bun:",pk,autoincrement" json:"id"`
		DestinationAreaID int64  `json:"destinationAreaId"`
		Nm                string `json:"nm"`
		Spot              string `json:"spot"`
		Kana1             string `json:"kana1"`
		Kana2             string `json:"kana2"`
		Snm1              string `json:"snm1"`
		Snm2              string `json:"snm2"`
		Dept1             string `json:"dept1"`
		Dept2             string `json:"dept2"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		DestinationArea *DestinationAreaEntity `bun:"rel:belongs-to,join:dest_area_id=id" json:"destinationArea"`
	}

	DestinationGroupEntity struct {
		bun.BaseModel `bun:"table:m_dest_group,alias:dg"`

		ID         int64  `bun:",pk,autoincrement" json:"id"`
		DestSpotID int64  `json:"destinationSpotId"`
		GroupID    int64  `json:"groupId"`
		DestCD     string `json:"destinationCD"`
		ShipperID  int64  `json:"shipperId"`
		Keisu      string `json:"keisu"`
		FareType   string `json:"fareType"`
		FareDist   string `json:"fareDist"`
		FarePrice  string `json:"farePrice"`
		Remarks    string `json:"remarks"`
		RomsID     int64  `json:"romsId"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		DestinationSpot *DestinationSpotEntity `bun:"rel:belongs-to,join:dest_spot_id=id" json:"destinationSpot"`
		Group           *GroupEntity           `bun:"rel:belongs-to,join:group_id=id" json:"group"`
	}

	JobEntity struct {
		bun.BaseModel `bun:"table:t_orders,alias:o"`

		ID       int64  `bun:",pk,autoincrement" json:"id"`
		RomsId   string `json:"romsId"`
		PickupID int64  `json:"pickupId"`
		DestCD   string `json:"destCd"`
		ItemID   int64  `json:"itemId"`
		ShipDate int64  `json:"shipDate"`
		DeliDate int64  `json:"fulfillDate"`
		SetTime  string `json:"setTime"`
		Volume1  int32
		Unit1    int32
		Volume2  int32
		Unit2    int32

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		DestinationGroup *DestinationGroupEntity `bun:"rel:belongs-to,join:dest_cd=dest_cd" json:"destinationGroup"`
		Item             *ItemEntity             `bun:"rel:belongs-to,join:item_id=id" json:"item"`
		Pickup           *PickupEntity           `bun:"rel:belongs-to,join:pickup_id=id" json:"pickup"`
	}

	VehicleEntity struct {
		bun.BaseModel `bun:"table:m_vehicles,alias:v"`

		ID                  int64  `bun:",pk,autoincrement" json:"id"`
		SerialNm            string `json:"name"`
		Number              string `json:"number"`
		NumberPlate         string `json:"numberPlate"`
		RomsID              int64  `json:"romsId"`
		FrontTankCapacity   int32
		RearTankCapacity    int32
		TotalTankCapacity   int32
		MaximumLoadCapacity int32

		CanHandle []int64 `bun:"-"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		MaterialCategories []ItemEntity `bun:"m2m:vehicle_material_categories,join:Vehicle=Item" json:"canHandle"`
	}

	ItemEntity struct {
		bun.BaseModel `bun:"table:m_items,alias:mc"`

		ID           int64 `bun:",pk,autoincrement"`
		Name         string
		RomsID       int64
		CleaningFlag bool `bun:"cleaning_flg"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`
	}

	VehicleMaterialCategoryEntity struct {
		bun.BaseModel `bun:"table:vehicle_material_categories,alias:vmc"`

		VehicleID int64          `bun:",pk"`
		Vehicle   *VehicleEntity `bun:"rel:belongs-to,join:vehicle_id=id"`
		ItemID    int64          `bun:",pk"`
		Item      *ItemEntity    `bun:"rel:belongs-to,join:item_id=id"`
	}

	DriverEntity struct {
		bun.BaseModel `bun:"table:m_drivers,alias:d"`

		ID              int64 `bun:",pk,autoincrement"`
		RomsID          int64
		Nm1             string
		Nm2             string
		Kana2           string
		Snm             string
		VehicleID       int64
		CanHandle       []int64 `bun:"-"`
		LeaveTakenDates []int64 `bun:"-"`

		CreatedAt int64
		CreatedBy int64
		UpdatedAt int64
		UpdatedBy int64
		DeletedAt time.Time `bun:",soft_delete,nullzero"`

		LeaveDates         []DriverLeaveDateEntity `bun:"rel:has-many,join:id=driver_id"`
		Vehicles           []VehicleEntity         `bun:"m2m:driver_vehicles,join:Driver=Vehicle"`
		MaterialCategories []ItemEntity            `bun:"m2m:driver_items,join:Driver=Item"`
	}

	DriverLeaveDateEntity struct {
		bun.BaseModel `bun:"table:m_driver_leave_dates,alias:ld"`

		DriverID int64
		Date     int64
	}

	DriverMaterialCategoryEntity struct {
		bun.BaseModel `bun:"table:driver_items,alias:dmc"`

		DriverID int64         `bun:",pk"`
		Driver   *DriverEntity `bun:"rel:belongs-to,join:driver_id=id"`
		ItemID   int64         `bun:",pk"`
		Item     *ItemEntity   `bun:"rel:belongs-to,join:item_id=id"`
	}

	DriverVehicle struct {
		bun.BaseModel `bun:"table:driver_vehicles,alias:dv"`

		DriverID  int64          `bun:",pk"`
		Driver    *DriverEntity  `bun:"rel:belongs-to,join:driver_id=id"`
		VehicleID int64          `bun:",pk"`
		Vehicle   *VehicleEntity `bun:"rel:belongs-to,join:vehicle_id=id"`
	}

	ProcessDetailsEntity struct {
		bun.BaseModel `bun:"table:process_details,alias:pd"`

		ID          int64 `bun:",pk,autoincrement"`
		Type        int
		Description string
		Start       time.Time
		End         time.Time
		Status      int
	}
)

func (j JobEntity) ToProblemJob() ProblemJob {
	return ProblemJob{
		ID:                 j.ID,
		PickupLocationID:   j.Pickup.ID,
		DeliveryLocationID: j.DestinationGroup.ID,
		ItemID:             j.Item.ID,
		FulfillDate:        j.ShipDate,
	}
}

func (v VehicleEntity) ToProblemVehicle() ProblemVehicle {
	return ProblemVehicle{
		ID:                v.ID,
		Name:              v.SerialNm,
		Color:             "",
		Capacity:          1,
		CapacityLiters:    1,
		WeightCapacity:    v.MaximumLoadCapacity,
		TotalTankCapacity: v.TotalTankCapacity,
		FrontTankCapacity: v.FrontTankCapacity,
		RearTankCapacity:  v.RearTankCapacity,
	}
}

func (d DriverEntity) ToProblemDriver() ProblemDriver {
	return ProblemDriver{
		ID:             d.ID,
		Name:           d.Nm1,
		PrimaryVehicle: d.VehicleID,
	}
}

func (v VehicleEntity) ToVehicleVM() VehicleVM {
	return VehicleVM{
		ID:            v.ID,
		RomsID:        fmt.Sprintf("%05d", v.RomsID),
		Name:          v.SerialNm,
		Color:         "",
		CanHandle:     "",
		CanHandleList: []string{},
		Time:          0,
	}
}

func toProblemCategory(mc ItemEntity, _ int) int64 {
	return mc.ID
}

func (mc ItemEntity) String() string {
	return mc.Name
}

func (mc ItemEntity) ToProblemCategory() ProblemItem {
	return ProblemItem{ID: mc.ID, Name: mc.Name}
}

func (loc LocationEntity) ToProblemLocation() ProblemLocation {
	latLon := strings.Split(loc.LatLng, ", ")
	lat, lon := "", ""
	if len(latLon) == 2 {
		lat, lon = latLon[0], latLon[1]
	}

	address := fmt.Sprintf("%s、%s、 %s、 %s", loc.Add1, loc.Add2, loc.Add3, loc.Zip)
	return ProblemLocation{
		ID: loc.ID,
		//LocationType:    loc.LocationType,
		//Description:     loc.Description,
		Address: address,
		Lat:     ParseStringToFloat64(lat),
		Lon:     ParseStringToFloat64(lon),
		//IsDepot:         loc.IsDepot,
		//ReadyTime:       loc.ReadyTime,
		//DueTime:         loc.DueTime,
		//ServiceDuration: loc.ServiceDuration,
	}
}

func ParseStringToFloat64(s string) float64 {
	value, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0
	}
	return value
}

type JobDto struct {
	ID                  int64
	RomsID              string
	PickupID            int64
	DestinationID       int64
	DestinationCd       string
	ItemID              int64
	ShipperID           int64
	ShipperName         string
	SetTime             string
	FulfillDate         int64
	PickupLocation      *LocationDto
	DestinationLocation *LocationDto
	Item                *ProblemItem
	Volume1             int32
	Unit1               int32
	Volume2             int32
	Unit2               int32
	DeletedAt           time.Time
	GroupID             int64
}

func (j JobDto) ToProblemJob() ProblemJob {

	// TODO need to discuss with Business
	var weight int32
	var litres int32
	if j.Unit1 == 1 {
		weight = j.Volume1
		litres = j.Volume2
	} else if j.Unit1 == 4 {
		weight = 0
		litres = j.Volume1
	}

	return ProblemJob{
		ID:                 j.ID,
		PickupLocationID:   j.PickupLocation.ID,
		DeliveryLocationID: j.DestinationLocation.ID,
		ItemID:             j.Item.ID,
		ShipperID:          j.ShipperID,
		FulfillDate:        j.FulfillDate,
		Weight:             weight,
		Litres:             litres,
	}
}

type LocationDto struct {
	ID        int64
	UniqueID  string
	Name      string
	ShortName string
	Lat       float64
	Lon       float64
	Address   string
}

func (loc LocationDto) ToProblemPickup() ProblemLocation {
	return ProblemLocation{
		ID:       loc.ID,
		UniqueID: loc.UniqueID,
		Address:  loc.Address,
		Lat:      loc.Lat,
		Lon:      loc.Lon,

		LocationType: 0,
		Description:  loc.Name,
		//IsDepot:         loc.IsDepot,
		ReadyTime:       32400000,
		DueTime:         70800000,
		ServiceDuration: 1200000,
	}
}

func (loc LocationDto) ToProblemDestination() ProblemLocation {
	return ProblemLocation{
		ID:       loc.ID,
		UniqueID: loc.UniqueID,
		Address:  loc.Address,
		Lat:      loc.Lat,
		Lon:      loc.Lon,

		LocationType: 1,
		Description:  loc.Name,
		//IsDepot:         loc.IsDepot,
		ReadyTime:       32400000,
		DueTime:         70800000,
		ServiceDuration: 1200000,
	}
}
