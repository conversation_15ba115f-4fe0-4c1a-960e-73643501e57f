package layouts

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
	"strings"
)

templ Main(content templ.Component) {
	<!DOCTYPE html>
	<html lang="jp" :data-bs-theme="dark ? 'dark': 'light'" x-data="{ dark: $persist(true) }">
		@head()
		<body class="layout-fixed layout-navbar-fixed layout-footer-fixed sidebar-expand-lg sidebar-mini bg-body-tertiary">
			<div class="app-wrapper">
				@nav()
				@aside()
				@appMain(content)
				@footer()
			</div>
		</body>
	</html>
}

templ Auth(content templ.Component) {
	<!DOCTYPE html>
	<html lang="jp" :data-bs-theme="dark ? 'dark': 'light'" x-data="{ dark: $persist(true) }">
		@head()
		<body class="login-page bg-body-secondary">
			@Messages()
			@content
		</body>
	</html>
}

templ Model(content templ.Component) {
	<div up-main data-csrf-token={ Page(ctx).CSRF }>
		<h3 class="mb-0">{ Page(ctx).Title }</h3>
		@content
	</div>
}

templ Plain(content templ.Component) {
	@appMain(content)
}

templ NoLayout(content templ.Component) {
	@Messages()
	@content
}

templ head() {
	<head>
		if Page(ctx).Title != "" {
			<title>{ Page(ctx).AppName } | { Page(ctx).Title } </title>
		} else {
			<title>{ Page(ctx).AppName } </title>
		}
		<link rel="icon" href={ File("favicon.ico") }/>
		<meta charset="utf-8"/>
		<meta name="viewport" content="width=device-width, initial-scale=1"/>
		<meta http-equiv="X-UA-Compatible" content="IE=edge"/>
		<link rel="stylesheet" href={ File("app.css") }/>
		<script type="module" src={ File("app.js") } defer></script>
	</head>
}

templ nav() {
	<nav class="app-header navbar navbar-expand bg-body">
		<div class="container-fluid">
			<ul class="navbar-nav">
				<li class="nav-item">
					<a class="nav-link" data-lte-toggle="sidebar" href="#" role="button">
						@Bicon("list")
					</a>
				</li>
			</ul>
			<ul class="navbar-nav ms-auto">
				<li class="nav-item">
					<a class="nav-link" href="#" data-lte-toggle="fullscreen">
						<i data-lte-icon="maximize" class="bi-arrows-fullscreen" style="display: block;"></i>
						<i data-lte-icon="minimize" class="bi-fullscreen-exit" style="display: none;"></i>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href="#">
						<i :class="dark? 'bi-moon' : 'bi-sun'" @click="dark = !dark"></i>
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" href={ GetUrl(ctx, types.RouteLogout) }>
						@Bicon("box-arrow-right")
					</a>
				</li>
			</ul>
		</div>
	</nav>
}

templ aside() {
	<aside id="sidebar" up-hungry class="app-sidebar bg-body-secondary shadow" data-bs-theme="dark">
		<div class="sidebar-brand">
			<a href="/" class="brand-link">
				<img src={ File("logo.png") } alt="LOMS" class="brand-image"/>
				<span class="brand-text fw-bolder">LOMS</span>
			</a>
		</div>
		<div class="sidebar-wrapper">
			<nav class="mt-2" up-nav>
				<ul class="nav sidebar-menu flex-column" data-lte-toggle="treeview" role="menu" data-accordion="false">
					@menu("solution", "play-fill", "車両、ドライバー　配車")
					@menu(types.RouteJobsByDate, "calendar4-event", "受注管理")
					<li class="nav-header">マスター</li>
					@menu("vehicles", "truck", "車両")
					@menu("drivers", "person", "乗務員")
					@menu("categories", "box", "材料カテゴリ")
					@menu("pickups", "geo", "積地")
				</ul>
			</nav>
		</div>
	</aside>
}

templ appMain(contents templ.Component) {
	<main class="app-main">
		<div up-main data-csrf-token={ Page(ctx).CSRF }>
			@Messages()
			<div class="app-content-header">
				<div class="container-fluid">
					<div class="row">
						<div class="col-sm-6">
							<h3 class="mb-0">{ Page(ctx).Title }</h3>
						</div>
						<div class="col-sm-6"></div>
					</div>
				</div>
			</div>
			<div class="app-content">
				<div class="container-fluid">
					@contents
				</div>
			</div>
		</div>
	</main>
}

templ menu(href, icon, text string) {
	<li class="nav-item">
		<a class="nav-link" href={ GetUrl(ctx, href) }>
			@Bicon(icon)
			<p class="text">{ text }</p>
		</a>
	</li>
}

templ footer() {
	<footer class="app-footer">
		<div class="float-end d-none d-sm-inline">
			<strong>
				@BiconWithText("git", ":")
			</strong> { Page(ctx).AppVersion }
			<strong>Env:</strong> <span>{ strings.ToUpper(Page(ctx).AppEnv) }</span>
		</div>
		<strong>
			<a href="#" class="text-decoration-none"></a>
		</strong>
	</footer>
}
