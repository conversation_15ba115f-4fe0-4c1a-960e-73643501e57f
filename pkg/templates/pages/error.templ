package pages

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Error() {
	if Page(ctx).StatusCode >= 500 {
		<p>Please try again.</p>
	} else if Page(ctx).StatusCode == 403 || Page(ctx).StatusCode == 401 {
		<p>You are not authorized to view the requested page.</p>
	} else if Page(ctx).StatusCode == 404 {
		<p>
			Click &nbsp;
			<a href={ GetUrl(ctx, types.RouteHome) } up-target="body">here</a>
			&nbsp; to return home 
		</p>
	} else {
		<p>Something went wrong</p>
	}
}
