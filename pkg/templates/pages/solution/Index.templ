package solution

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
	"strings"
)

templ Validate(data *types.SolutionIndexData, validate string) {
	if validate == "date" {
		@Runs(data.Runs)
		@Solutions(data.Solutions)
	}
	if validate == "runId" {
		@Solutions(data.Solutions)
	}
}

templ Index(data *types.SolutionIndexData) {
	<div class="card-group">
		<div class="card">
			<div class="card-header">
				<div class="card-title">データを取得</div>
			</div>
			<div class="card-body">
				<form class="row g-3" up-history="auto">
					<div class="col-md-6 mb-3">
						<label for="date" class="form-label">日付</label>
						<input id="date" type="date" name="date" value={ S(data.Date) } class="form-control" up-validate="#runs, #solutions, #solution-details"/>
					</div>
					@Runs(data.Runs)
					@Solutions(data.Solutions)
				</form>
			</div>
		</div>
		<div id="run-details" up-defer up-href={ S(GetUrl(ctx, types.RouteRunDetails, data.Date)) }>
			Loading...
		</div>
	</div>
	<div class="mt-4">
		<div id="solution-details"></div>
	</div>
	<div id="map-container" up-defer up-href={ S(GetUrl(ctx, types.RouteMap)) }></div>
}

templ Runs(runs []types.RunDropDownData) {
	<div class="col-md-6 mb-3" id="runs">
		<label for="runId" class="form-label">配車: { S(len(runs)) }</label>
		<select name="runId" class="form-select" id="runId" up-validate="#solutions, #solution-details">
			<option value="0">選択してください</option>
			for _, it := range runs {
				<option data-run-id={ S(it.RunID) } value={ S(it.RunID) }>{ it.Text }</option>
			}
		</select>
	</div>
}

templ Solutions(data []types.SolutionDropDownData) {
	<div class="col-md-12 mb-3" id="solutions">
		<label for="solutionId" class="form-label">配車結果: { S(len(data)) }</label>
		<select id="solutionId" name="solutionId" class="form-select" up-validate="#solution-details">
			<option value="0">選択してください</option>
			for _, it := range data {
				<option data-solution-id={ S(it.ID) } value={ S(it.ID) }>
					if !it.IsManual {
						{ ToTime(it.TotalTime) }, Score: { it.Score }
					}
					if it.IsManual {
						「手動で変更されました」
					} else {
						解析時間: { ToMinutesSeconds(it.TimeToCreate) }
					}
				</option>
			}
		</select>
	</div>
}

func containsString(inputString string, searchString string) bool {
	return strings.Contains(inputString, searchString)
}
