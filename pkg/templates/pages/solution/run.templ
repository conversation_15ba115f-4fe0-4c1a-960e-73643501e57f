package solution

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Run(data *types.SolutionRunData) {
	<div
		class="card"
		id="run-details"
		if data.SolverInProgress {
			up-poll
			up-interval="2000"
			up-source={ S(GetUrl(ctx, types.RouteRunDetails, data.Date)) }
		}
	>
		<div class="card-header">
			<div class="card-title">{ data.Title }</div>
		</div>
		<div class="card-body">
			if data.SolverInProgress {
				<h3 class="text-success">配車中</h3>
			} else {
				<form action={ GetUrl(ctx, types.RouteSolve) } method="post" up-target="#run-details">
					@CSRF()
					<div class="col-md-6 mb-3">
						<label for="solve_date" class="form-label">日付</label>
						<input id="solve_date" class="form-control" type="date" name="date" value={ data.Date }/>
					</div>
					<div class="container">
						<div class="row">
							<div class="col-md-6 mb-3 form-check">
								<input id="solve_with_history" type="checkbox" name="withHistory" class="form-check-input" value="true"/>
								<label for="solve_with_history" class="form-check-label">過去のデータを考慮する</label>
							</div>
							<div class="col-md-6 mb-3 form-check">
								<input id="solve_with_even_allocation" type="checkbox" name="withEvenAllocation" class="form-check-input" value="true"/>
								<label for="solve_with_even_allocation" class="form-check-label">均等配車</label>
							</div>
						</div>
					</div>
					<button type="submit" class="btn btn-success">配車を開始</button>
				</form>
			}
		</div>
	</div>
}
