package solution

import "loms/pkg/types"

templ Map(data *types.SolutionMapInput) {
	<div id="map-container">
		<script src={ data.GetMapUrl() }></script>
		<div id="map-loading" class="text-center mt-3">
			for _, c := range []string{"primary", "secondary", "success", "danger", "warning", "info", "dark"} {
				<div class={ "spinner-grow text-" + c } role="status">
					<span class="visually-hidden">Loading...</span>
				</div>
			}
		</div>
		<div
			id="map"
			class="mt-3"
			data-api-url={ data.ApiUrl }
			data-customer-id={ data.CustomerId }
			data-signature={ data.Signature }
			data-request-code={ data.RequestCode }
			data-to-be-loded-from={ data.ToBeLoadedFrom }
		></div>
	</div>
}
