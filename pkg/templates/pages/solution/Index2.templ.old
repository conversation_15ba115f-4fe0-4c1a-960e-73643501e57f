package solution

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
	"strings"
)

templ Validate(data *types.SolutionIndexData, validate string) {
	if validate == "date" {
		@Runs(data)
		@Solutions(data)
	}
	if validate == "runId" {
		@Solutions(data)
	}
}

templ Index(data *types.SolutionIndexData) {
	<div class="card-group">
		<div class="card">
			<div class="card-header">
				<div class="card-title">データを取得</div>
			</div>
			<div class="card-body">
				<form class="row g-3">
					<div class="col-md-6 mb-3">
						<label for="date" class="form-label">日付</label>
						<input id="date" type="date" name="date" value={ S(data.Date) } class="form-control" up-validate="#runs, #solutions, #solution-details"/>
					</div>
					@Runs(data)
					@Solutions(data)
				</form>
			</div>
		</div>
		<div id="run-details" up-defer up-href={ S(GetUrl(ctx, types.RouteRunDetails, data.Date)) }>
			Loading...
		</div>
	</div>
	<div class="mt-4">
		<div id="solution-details"></div>
	</div>
	<!-- <div id="map-container" up-defer up-href={ S(GetUrl(ctx, types.RouteMap)) }></div> -->
}

templ Runs(data *types.SolutionIndexData) {
	<div class="col-md-6 mb-3" id="runs">
		<label for="runId" class="form-label">配車: { S(len(data.Runs)) }</label>
		<select name="runId" class="form-select" id="runId" up-validate="#solutions, #solution-details">
			<option value="0">選択してください</option>
			for _, it := range data.Runs {
				<option data-run-id={ S(it) } value={ S(it) }>{ S(it) }</option>
			}
		</select>
	</div>
}

templ Solutions(data *types.SolutionIndexData) {
	<div class="col-md-12 mb-3" id="solutions">
		<label for="solutionId" class="form-label">配車結果: { S(len(data.Solutions)) }</label>
		<select id="solutionId" name="solutionId" class="form-select" up-validate="#solution-details">
			<option value="0">選択してください</option>
			for _, it := range data.Solutions {
				<option data-solution-id={ S(it.ID) } value={ S(it.ID) }>{ ToTime(it.TotalTime) }, Score: { it.Score }</option>
			}
		</select>
	</div>
}

func containsString(inputString string, searchString string) bool {
	return strings.Contains(inputString, searchString)
}
