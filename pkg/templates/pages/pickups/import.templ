package pickups

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Import(msg string, errs []string) {
	<form action={ GetUrl(ctx, types.RouteCoordinatesUpdateImportPost) } method="post" up-autosubmit>
		@Alert("warning", msg)
		@CSRF()
		<div class="mb-3">
			<label for="file" class="form-label">CSV ファイル</label>
			<input class="form-control" name="file" id="file" type="file"/>
		</div>
		if len(errs) > 0 {
			<ul class="list-group list-group-numbered">
				for _, it := range errs {
					<li class="list-group-item list-group-item-warning">{ it }</li>
				}
			</ul>
		}
	</form>
}
