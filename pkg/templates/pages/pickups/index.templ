package pickups

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Index(page *types.Page[*types.CoordinatesUpdateForm]) {
	@AddLink(GetUrl(ctx, types.RoutePickupsAddNew))
	<a href={ GetUrl(ctx, types.RouteCoordinatesUpdateImport) } class="btn btn-outline-primary mb-3" up-layer="new" up-history="false" up-on-dismissed="up.reload('.table')">
		@BiconWithText("upload", "緯度経度を更新のCSVインポート")
	</a>
	@Table() {
		@THead() {
			<th>#</th>
			@Sortable(SProps("p.Roms_ID", "RomsID", "", page.Paging))
			@Sortable(SProps("p.nm", "積地", "", page.Paging))
			@Sortable(SProps("location.Add1", "Add1", "", page.Paging))
			@Sortable(SProps("location.Add2", "Add2", "", page.Paging))
			@Sortable(SProps("location.Add3", "Add3", "", page.Paging))
			@Sortable(SProps("location.Lat_Lng", "緯度経度", "", page.Paging))
		}
		<tbody>
			for no, entity := range page.Content {
				<tr>
					<td>{ S((no + ( page.Paging.Size * (page.Paging.Page-1)))+ 1 ) } </td>
					<td>{ S(entity.RomsID) }</td>
					@EditLinkTd(types.RoutePickupsDetails, S(entity.ID), entity.Name)
					<td>{ entity.Add1 }</td>
					<td>{ entity.Add2 }</td>
					<td>{ entity.Add3 }</td>
					<td>{ entity.LatLng }</td>
				</tr>
			}
		</tbody>
	}
	@Pagination(PProps("", page.TotalPages(), page.Paging))
}
