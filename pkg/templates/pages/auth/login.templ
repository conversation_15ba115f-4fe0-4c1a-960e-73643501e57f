package auth

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Login(form *types.LoginForm) {
	<div class="login-box">
		<div class="card">
			<form method="post" action={ GetUrl(ctx, types.RouteLoginSubmit) } up-target="body">
				<div class="card-header">
					<h1 class="mb-0">
						<img src={ File("logo.png") } alt="LOMS"/>
						<b>LOMS</b> ログイン
					</h1>
				</div>
				<div class="card-body login-card-body" up-main="root">
					@Field(FProps("Email", "メールアドレス", form.Email, form).WithType("email").Focused())
					@Field(FProps("Password", "パスワード", form.Password, form).WithType("password"))
					@CSRF()
				</div>
				<div class="card-footer">
					<div class="d-flex align-items-end justify-content-between">
						<a class="btn btn-outline-secondary d-grid gap-2" up-target=".card" href={ GetUrl(ctx, types.RouteRegister) }>ユーザー登録</a>
						<div class="d-grid gap-2"><button type="submit" class="btn btn-info">ログイン</button> </div>
					</div>
				</div>
			</form>
		</div>
	</div>
}
