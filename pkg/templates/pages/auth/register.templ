package auth

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Register(form *types.RegisterForm) {
	<div class="register-box">
		<div class="card">
			<form method="post" hx-boost="true" action={ GetUrl(ctx, types.RouteRegisterSubmit) } up-target="body">
				<div class="card-header">
					<h1 class="mb-0">
						<img src={ File("logo.png") } alt="LOMS"/>
						<b>LOMS</b> ユーザー登録
					</h1>
				</div>
				<div class="card-body login-card-body" up-main="root">
					@Field(FProps("Name", "ユーザー名", form.Name, form).Focused())
					@Field(FProps("Email", "メールアドレス", form.Email, form).WithType("email"))
					@Field(FProps("Password", "パスワード", form.Password, form).WithType("password"))
					@Field(FProps("ConfirmPassword", "パスワード再入力", form.ConfirmPassword, form).WithType("password"))
					@CSRF()
				</div>
				<div class="card-footer">
					<div class="d-flex align-items-end justify-content-between">
						<a href={ GetUrl(ctx, types.RouteLogin) } up-target=".card">ログイン</a>
						<div class="d-grid gap-2"><button type="submit" class="btn btn-info">登録</button> </div>
					</div>
				</div>
			</form>
		</div>
	</div>
}
