package auth

import (
	"fmt"
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ ResetPassword(form *types.ResetPasswordForm) {
	<form method="post" hx-boost="true" action={ templ.URL(Page(ctx).Path) }>
		<div class="field">
			<label for="password" class="label">Password</label>
			<div class="control">
				<input id="password" type="password" name="password" placeholder="*******" class={ fmt.Sprintf("input %s", form.GetFieldStatusClass("Password")) }/>
				@FieldErrors(form.GetFieldErrors("Password"))
			</div>
		</div>
		<div class="field">
			<label for="password-confirm" class="label">Confirm password</label>
			<div class="control">
				<input type="password" id="password-confirm" name="password-confirm" placeholder="*******" class={ fmt.Sprintf("input %s", form.GetFieldStatusClass("ConfirmPassword")) }/>
				@FieldErrors(form.GetFieldErrors("ConfirmPassword"))
			</div>
		</div>
		<div class="field is-grouped">
			<p class="control">
				<button class="button is-primary">Update password</button>
			</p>
		</div>
		@CSRF()
	</form>
}
