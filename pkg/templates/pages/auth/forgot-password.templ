package auth

import (
	"fmt"
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ ForgotPassword(form *types.ForgotPasswordForm) {
	<form method="post" hx-boost="true" action={ GetUrl(ctx, types.RouteForgotPasswordSubmit) }>
		<div class="content">
			<p>Enter your email address and we'll email you a link that allows you to reset your password.</p>
		</div>
		<div class="field">
			<label for="email" class="label">Email address</label>
			<div class="control">
				<input id="email" name="email" type="email" class={ fmt.Sprintf("input %s", form.Submission.GetFieldStatusClass("Email")) } value={ form.Email }/>
				@FieldErrors(form.Submission.GetFieldErrors("Email"))
			</div>
		</div>
		<div class="field is-grouped">
			<p class="control">
				<button class="button is-primary">Reset password</button>
			</p>
			<p class="control">
				<a href={ GetUrl(ctx, types.RouteHome) } class="button is-light">Cancel</a>
			</p>
		</div>
		@CSRF()
	</form>
}
