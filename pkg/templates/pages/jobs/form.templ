package jobs

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ JobForm(form *types.JobForm, pickups []types.Option, deliveries []types.Option, categories []types.Option) {
	@Form(FrmProps(form, types.RouteJobUpdate, form.ID)) {
		@HiddenID(form.ID)
		@Select(FProps("PickupID", "積地", "", form), pickups, false)
		@Select(FProps("DeliveryID", "届け先", "", form), deliveries, false)
		@Select(FProps("MaterialCategoryID", "材料カテゴリ", "", form), categories, false)
		@Field(FProps("FulfillDate", "日付", form.FulfillDate, form).WithType("date"))
	}
}
