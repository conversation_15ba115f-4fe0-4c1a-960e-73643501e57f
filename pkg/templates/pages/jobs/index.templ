package jobs

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Index(page *types.Page[*types.JobForm]) {
	@AddLink(GetUrl(ctx, types.RouteJobAddNew))
	@ImportLink(GetUrl(ctx, types.RouteJobImport))
	@Table() {
		@THead() {
			@Sortable(SProps("id", "ID", "", page.Paging))
			@Sortable(SProps("Pickup_ID", "積地", "", page.Paging))
			@Sortable(SProps("Delivery_ID", "届け先", "", page.Paging))
			@Sortable(SProps("Material_Category_ID", "材料カテゴリ", "", page.Paging))
			@Sortable(SProps("Fulfill_Date", "日付", "", page.Paging))
		}
		<tbody>
			for _, entity := range page.Content {
				<tr>
					@EditLinkTd(types.RouteJobDetails, S(entity.ID), S(entity.ID))
					@EditLinkTd(types.RouteJobDetails, S(entity.ID), entity.Pickup)
					<td>{ entity.Delivery }</td>
					<td>{ entity.MaterialCategory }</td>
					<td>{ entity.FulfillDate }</td>
				</tr>
			}
		</tbody>
	}
	@Pagination(PProps("", page.TotalPages(), page.Paging))
}
