package jobs

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ ByDate(jobs []types.JobDto, data *types.JobIndexData) {
	<div class="mt-4">
		<div id="solution-details">
			<form action={ GetUrl(ctx, types.RouteJobsByDate) } class="mb-4">
				<div class="col-md-6 mb-3">
					<label for="solve_date" class="form-label">日付</label>
					<input id="solve_date" class="form-control" type="date" name="date" value={ data.Date }/>
				</div>
				<button type="submit" class="btn btn-success">取得</button>
			</form>
			<h5>注文数: { S(len(jobs)) }</h5>
			@Table() {
				<tr>
					<th>#</th>
					<th>ID</th>
					<th>RomsID</th>
					<th>荷主</th>
					<th>Pickup UniqueID</th>
					<th>積地</th>
					<th>品名</th>
					<th>Delivery UniqueID</th>
					<th>届先</th>
					<th>数量 容量</th>
					<th>指定 時間</th>
				</tr>
				for idx, job := range jobs {
					<tr>
						<td>{ S(idx + 1) }</td>
						<td>{ S(job.ID) }</td>
						<td>{ job.RomsID }</td>
						<td>{ job.ShipperName }</td>
						<td>{ job.PickupLocation.UniqueID }</td>
						<td>{ job.PickupLocation.ShortName }</td>
						<td>{ job.Item.Name }</td>
						<td>{ job.DestinationLocation.UniqueID }</td>
						<td>{ job.DestinationLocation.ShortName }</td>
						<td>
							{ S(job.Volume1) }
							if job.Unit1 == 1 {
								KG
							} else if job.Unit1 == 5 {
								T
							} else {
								「{ S(job.Unit1) }」
							}
						</td>
						<td>{ job.SetTime }</td>
					</tr>
				}
			}
		</div>
	</div>
}
