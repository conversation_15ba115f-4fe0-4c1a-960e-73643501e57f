package categories

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Index(page *types.Page[*types.MaterialCategoryForm]) {
	@AddLink(GetUrl(ctx, types.RouteCategoriesAddNew))
	@Table() {
		@THead() {
			@Sortable(SProps("id", "ID", "", page.Paging))
			@Sortable(SProps("Name", "材料カテゴリ", "", page.Paging))
			@Sortable(SProps("Roms_ID", "RomsID", "", page.Paging))
			<th></th>
		}
		<tbody>
			for _, entity := range page.Content {
				<tr>
					@EditLinkTd(types.RouteCategoriesDetails, S(entity.ID), S(entity.ID))
					@EditLinkTd(types.RouteCategoriesDetails, S(entity.ID), entity.Name)
					<td>{ S(entity.RomsID) }</td>
					@DeleteLinkTd(types.RouteCategoriesDelete, S(entity.ID), entity.Name)
				</tr>
			}
		</tbody>
	}
	@Pagination(PProps("", page.TotalPages(), page.Paging))
}
