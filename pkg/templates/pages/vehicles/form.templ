package vehicles

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ VehiclesForm(form *types.VehicleForm, categories []types.Option) {
	@Form(FrmProps(form, types.RouteVehiclesUpdate, form.ID)) {
		@HiddenID(form.ID)
		@Field(FProps("Name", "材料カテゴリ", form.Name, form).Focused())
		@Select(FProps("CanHandle", "対応可能", "", form), categories, true)
		@Field(FProps("Color", "色", form.Color, form))
		@Field(FProps("Capacity", "容量", S(form.Capacity), form))
		@Field(FProps("CapacityLiters", "容量（リットル）", S(form.CapacityLiters), form))
	}
}
