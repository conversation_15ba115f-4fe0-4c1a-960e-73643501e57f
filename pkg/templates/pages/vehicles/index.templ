package vehicles

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ Index(page *types.Page[*types.VehicleForm]) {
	@AddLink(GetUrl(ctx, types.RouteVehiclesAddNew))
	@Table() {
		@THead() {
			@Sortable(SProps("id", "ID", "", page.Paging))
			@Sortable(SProps("Name", "車両", "", page.Paging))
			<th>対応可能</th>
		}
		<tbody>
			for _, entity := range page.Content {
				<tr>
					@EditLinkTd(types.RouteVehiclesDetails, S(entity.ID), S(entity.ID))
					@EditLinkTd(types.RouteVehiclesDetails, S(entity.ID), entity.Name)
					<td>{ entity.CanHandleNames }</td>
				</tr>
			}
		</tbody>
	}
	@Pagination(PProps("", page.TotalPages(), page.Paging))
}
