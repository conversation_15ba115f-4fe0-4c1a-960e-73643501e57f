package drivers

import (
	. "loms/pkg/templates/tags"
	"loms/pkg/types"
)

templ DriversForm(form *types.DriverForm, categories []types.Option) {
	@Form(FrmProps(form, types.RouteDriversUpdate, form.ID)) {
		@HiddenID(form.ID)
		@Field(FProps("Name", "材料カテゴリ", form.Name, form).Focused())
		@Field(FProps("LeaveDates", "休暇取得日", form.LeaveDates, form))
		@Select(FProps("CanHandle", "対応可能", "", form), categories, true)
	}
}
