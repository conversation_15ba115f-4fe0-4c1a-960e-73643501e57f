package tags

import (
	"fmt"
	"loms/framework/form"
	"loms/pkg/types"
)

templ Form(props FormProps) {
	<form
		action={ GetUrl(ctx, props.UrlName, props.Params) }
		method="post"
		class="row g-3"
		autocomplete="off"
		if props.UpLayer != "" {
			up-layer={ props.UpLayer }
		}
	>
		@CSRF()
		@Messages()
		{ children... }
		if props.AddButtons {
			<div class="d-grid gap-2 d-md-flex justify-content-md-end">
				<button type="reset" class="btn btn-secondary">{ props.Reset }</button>
				<button type="submit" class="btn btn-primary">{ props.Submit }</button>
			</div>
		}
	</form>
}

templ CSRF() {
	<input type="hidden" name="csrf" value={ Page(ctx).CSRF }/>
}

templ HiddenID(id int64) {
	<input type="hidden" name="ID" value={ S(id) }/>
}

templ FieldErrors(errors []string) {
	for _, error := range errors {
		<div class="invalid-feedback">{ error }</div>
	}
}

templ Label(name, label string) {
	<label for={ name } class="form-label">{ label }</label>
}

templ Field(props FieldProps) {
	@DivWithColumns(props.Cols) {
		@Label(props.Name, props.Label)
		<input
			id={ props.Name }
			name={ props.Name }
			type={ props.Typ }
			class={ className(props) }
			value={ props.Value }
			if props.Focus {
				autofocus
			}
		/>
		@FieldErrors(CleanErrors(props))
	}
}

templ Select(props FieldProps, options []types.Option, isMultiple bool) {
	@DivWithColumns(props.Cols) {
		@Label(props.Name, props.Label)
		<select
			placeholder="選択してください"
			id={ props.Name }
			name={ props.Name }
			class={ className(props.WithType("select")) }
			if isMultiple {
				multiple
			}
			if props.Focus {
				autofocus
			}
		>
			<option value="">選択してください</option>
			for _, it := range options {
				<option
					value={ S(it.ID) }
					if it.IsChecked {
						selected
					}
				>{ it.Value }</option>
			}
		</select>
		@FieldErrors(CleanErrors(props))
	}
}

templ CheckBox(props FieldProps) {
	<div class="form-check">
		<input
			id={ props.Name + "_" + props.Label }
			name={ props.Name }
			type={ props.Typ }
			class={ className(props) }
			value={ props.Value }
			if props.Checked {
				checked
			}
		/>
		<label class="form-check-label" for={ props.Name + "_" + props.Label }>{ props.Label }</label>
	</div>
}

func columnClass(cols int) string {
	return fmt.Sprintf("col-md-%d mt-3", cols)
}

func className(props FieldProps) string {
	errors := CleanErrors(props)
	baseClass := "form-control"
	if props.Typ == "checkbox" || props.Typ == "radio" {
		baseClass = "form-check-input"
	} else if props.Typ == "select" {
		baseClass = "form-control tom-select"
	}
	if len(errors) == 0 {
		return baseClass
	}
	return baseClass + " is-invalid"
}

type FieldProps struct {
	Cols    int
	Name    string
	Label   string
	Typ     string
	Value   string
	Form    form.Form
	Focus   bool
	Checked bool
}

func FProps(name, label string, value string, form form.Form) FieldProps {
	return FieldProps{Typ: "text", Cols: 12, Name: name, Label: label, Value: value, Form: form}
}

func (f FieldProps) WithType(typ string) FieldProps {
	f.Typ = typ
	return f
}

func (f FieldProps) AsCheckbox(typ string, checked bool) FieldProps {
	f.Typ = typ
	f.Checked = checked
	return f
}

func (f FieldProps) WithCols(cols int) FieldProps {
	f.Cols = cols
	return f
}

func (f FieldProps) Focused() FieldProps {
	f.Focus = true
	return f
}

func CleanErrors(props FieldProps) []string {
	errors := props.Form.GetFieldErrors(props.Name)
	var cleanedErrors []string
	for _, err := range errors {
		if err != "" {
			cleanedErrors = append(cleanedErrors, err)
		}
	}
	return cleanedErrors
}

type FormProps struct {
	Form       form.Form
	UrlName    string
	Params     []any
	Method     string
	UpLayer    string
	AddButtons bool
	Reset      string
	Submit     string
}

func FrmProps(form form.Form, urlName string, params ...any) FormProps {
	return FormProps{
		Form:       form,
		UrlName:    urlName,
		Params:     params,
		Method:     "post",
		Reset:      "リセット",
		Submit:     "登録",
		AddButtons: true,
	}
}
