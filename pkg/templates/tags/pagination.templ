package tags

import (
	"fmt"
	"loms/pkg/types"
)

templ Pagination(props PagingProps) {
	if props.TotalPages > 1 {
		<nav aria-label="Page navigation">
			<ul class="pagination">
				<li
					if props.Page.Page == 1 {
						class="page-item disabled"
					} else {
						class="page-item"
					}
				>
					<a
						class="page-link"
						href={ props.BuildPreviousPageUrl() }
						aria-label="Previous"
						title="Previous Page"
						rel="tooltip"
					>
						<span aria-hidden="true">&laquo;</span>
					</a>
				</li>
				for _, i := range props.Page.Numbers(props.TotalPages) {
					<li class="page-item{#if props.page == i} active{/if}">
						<a class="page-link" href={ props.BuildPageUrl(i) } title="Page {i}" rel="tooltip">{ S(i) }</a>
					</li>
				}
				if props.Page.HasMore(props.TotalPages) {
					<li class="page-item disabled">
						<a class="page-link" href="#">
							<span data-feather="more-horizontal" width="20" height="20"></span>...
						</a>
					</li>
				}
				<li
					if props.Page.Page == props.TotalPages {
						class="page-item disabled"
					} else {
						class="page-item"
					}
				>
					<a
						class="page-link"
						href={ props.BuildNextPageUrl() }
						aria-label="Next"
						title="Next Page"
						rel="tooltip"
					>
						<span aria-hidden="true">&raquo;</span>
					</a>
				</li>
			</ul>
		</nav>
	}
}

type PagingProps struct {
	URL        string
	Page       *types.PageRequest
	TotalPages int
}

func PProps(url string, totalPages int, page *types.PageRequest) PagingProps {
	return PagingProps{URL: url, TotalPages: totalPages, Page: page}
}

func (p *PagingProps) BuildPreviousPageUrl() templ.SafeURL {
	return templ.SafeURL(fmt.Sprintf("%s?size=%d&page=%d&sort=%s&order=%s", p.URL, p.Page.Size, p.Page.PreviousPage(), p.Page.Sort, p.Page.Order))
}

func (p *PagingProps) BuildNextPageUrl() templ.SafeURL {
	return templ.SafeURL(fmt.Sprintf("%s?size=%d&page=%d&sort=%s&order=%s", p.URL, p.Page.Size, p.Page.NextPage(p.TotalPages), p.Page.Sort, p.Page.Order))
}

func (p *PagingProps) BuildPageUrl(page int) templ.SafeURL {
	return templ.SafeURL(fmt.Sprintf("%s?size=%d&page=%d&sort=%s&order=%s", p.URL, p.Page.Size, page, p.Page.Sort, p.Page.Order))
}
