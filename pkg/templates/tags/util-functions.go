package tags

import (
	"context"
	"fmt"
	"loms/config"
	"loms/framework/funcs"
	"loms/framework/page"
	"loms/pkg/types"
	"time"

	"github.com/a-h/templ"
	"github.com/labstack/gommon/random"
)

var (
	// CacheBuster stores a random string used as a cache buster for static files.
	CacheBuster = random.String(10)
	S           = fmt.Sprint
)

func IsEqualValue(item, expected, val string) string {
	if item == expected {
		return val
	}
	return ""
}

func Page(ctx context.Context) *page.Page {
	return ctx.Value(types.TemplCtxKeyPage).(*page.Page)
}

// File appends a cache buster to a given filepath so it can remain cached until the app is restarted
func File(filepath string) string {
	return fmt.Sprintf("/%s/%s?v=%s", config.StaticPrefix, filepath, CacheBuster)
}

func GetUrl(ctx context.Context, name string, params ...any) templ.SafeURL {
	funcs := ctx.Value(types.TemplCtxKeyFuncs).(*funcs.Funcs)
	return funcs.URL(name, params...)
}

func ToTime(totalTime int64) string {
	hours := totalTime / int64(time.Hour/time.Millisecond)
	minutes := (totalTime / int64(time.Minute/time.Millisecond)) % int64(time.Hour/time.Minute)
	return fmt.Sprintf("%02d:%02d", hours, minutes)
}

func ToMinutesSeconds(totalSeconds int64) string {
	minutes := totalSeconds / 60
	seconds := totalSeconds % 60
	return fmt.Sprintf("%02d分%02d秒", minutes, seconds)
}
