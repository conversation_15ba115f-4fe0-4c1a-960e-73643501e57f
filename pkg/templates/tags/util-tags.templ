package tags

import "fmt"

templ BiconWithText(name, text string) {
	<i class={ fmt.Sprintf("bi-%s", name) }></i> { text }
}

templ Bicon(name string) {
	<i class={ fmt.Sprintf("bi-%s", name) }></i>
}

templ Table() {
	<table class="table table-striped table-hover table-bordered">
		{ children... }
	</table>
}

templ THead() {
	<thead class="table-success">
		<tr>
			{ children... }
		</tr>
	</thead>
}

templ DivWithColumns(columns int) {
	<div class={ "col-" + S(columns) }>
		{ children... }
	</div>
}
