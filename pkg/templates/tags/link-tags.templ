package tags

import (
	"fmt"
	"loms/pkg/types"
)

templ AddLink(url templ.SafeURL) {
	<a href={ url } class="btn btn-outline-primary mb-3 me-3" up-layer="new" up-history="false" up-on-dismissed="up.reload('.table')">
		@BiconWithText("plus-lg", "新規登録")
	</a>
}

templ ImportLink(url templ.SafeURL) {
	<a href={ url } class="btn btn-outline-primary mb-3" up-layer="new" up-history="false" up-on-dismissed="up.reload('.table')">
		@BiconWithText("upload", "CSV インポート")
	</a>
}

templ EditLinkTd(url, id, label string) {
	<td>
		<a href={ GetUrl(ctx, url, id) } up-history="false" up-layer="new" up-on-dismissed="up.reload('.table')">{ label }</a>
	</td>
}

templ DeleteLinkTd(url, id, label string) {
	<td>
		<a up-method="delete" href={ GetUrl(ctx, url, id) } up-confirm="Are you sure?" up-history="false">削除</a>
	</td>
}

templ Sortable(props SortProps) {
	<th>
		<a href={ props.BuildUrl() }>
			{ props.Label }
		</a>
		if props.Page.GetCssClass(props.Name) != "" {
			<i class={ fmt.Sprintf("text-green bi-%s", props.Page.GetCssClass(props.Name)) }></i>
		}
	</th>
}

type SortProps struct {
	Name       string
	Label      string
	URL        string
	Page       *types.PageRequest
	TotalPages int
}

func SProps(name, label, url string, page *types.PageRequest) SortProps {
	return SortProps{Name: name, Label: label, URL: url, Page: page}
}

func (s SortProps) BuildUrl() templ.SafeURL {
	return templ.SafeURL(fmt.Sprintf("%s?size=%d&page=%d&sort=%s&order=%s", s.URL, s.Page.Size, s.Page.Page, s.Name, s.Page.NextOrder(s.Name)))
}

/*
func (s SortProps) BuildPageUrl() templ.SafeURL {
	return templ.SafeURL(fmt.Sprintf("%s?size=%d&page=%d&sort=%s&order=%s", s.URL, s.Page.Size, s.Page.Page, s.Name, s.Page.Sort))
}

func (s SortProps) WithTotalPages(totalPages int) SortProps {
	s.TotalPages = totalPages
	return s
}
*/
