package tags

import "fmt"

templ Alert(typ string, text string) {
	if len(text) > 0 {
		<div class={ fmt.Sprintf("alert alert-%s alert-dismissible", typ) } role="alert">
			@templ.Raw(text)
			<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
		</div>
	}
}

templ message(typ, text string) {
	<div class="toast" role="alert" data-type={ typ } data-text={ text }></div>
}

templ Messages() {
	<div up-flashes="">
		for _, msg := range Page(ctx).GetMessages("success") {
			@message("success", msg)
		}
		for _, msg := range Page(ctx).GetMessages("info") {
			@message("info", msg)
		}
		for _, msg := range Page(ctx).GetMessages("warning") {
			@message("warning", msg)
		}
		for _, msg := range Page(ctx).GetMessages("danger") {
			@message("error", msg)
		}
	</div>
}
