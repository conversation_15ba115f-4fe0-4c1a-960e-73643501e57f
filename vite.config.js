import { defineConfig } from 'vite';

export default defineConfig({
  publicDir: 'client/public',
  build: {
	  minify: false,
	  terserOptions: {
		  compress: false,
		  mangle: false,
	  },
    outDir: 'pkg/static',
    assetsDir: '',
    rollupOptions: {
      input: '/client/app.js', // Specify the main JS file
      output: {
        entryFileNames: '[name].js',
        chunkFileNames: '[name].js',
        assetFileNames: '[name].[ext]' // Separate CSS files
      }
    }
  }
});
