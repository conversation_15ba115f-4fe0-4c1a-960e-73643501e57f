import persist from '@alpinejs/persist'
import '@popperjs/core'
import 'bootstrap/dist/js/bootstrap.min.js';
import "admin-lte/dist/js/adminlte.min.js";
import Alpine from 'alpinejs';

import './app.css'
import $ from 'jquery';
import Swal from 'sweetalert2/dist/sweetalert2.all.min.js';
import TomSelect from 'tom-select/dist/js/tom-select.complete.min.js';
import 'unpoly/unpoly.min.js';

import * as vis from 'vis-timeline/standalone';

window.Alpine = Alpine
Alpine.plugin(persist)
Alpine.start()

up.protocol.config.csrfToken = function (){
	let element = document.querySelector('[data-csrf-token]')
	if(!element) return "";
	return element.dataset.csrfToken
}
up.fragment.config.mainTargets.push('main')
up.feedback.config.currentClasses = ['active']
up.link.config.followSelectors.push('a[href]');
up.form.config.submitSelectors.push(['form']);
up.fragment.config.navigateOptions.transition = 'cross-fade';
up.fragment.config.navigateOptions.cache = false;
if (window.location.hostname.toLowerCase() === 'localhost') {
	up.log.enable();
} else {
	up.log.disable();
}
window.navitimeConfig = { apiUrl: '', customerId: '', signature: '', requestCode: '', toBeLoadedFrom: '' };
let mapSelector = '#map';
up.compiler(mapSelector, function (element, data) {
	initMap(data)
});


let Toast = Swal.mixin({
	toast: true,
	position: "top-end",
	showConfirmButton: false,
	timer: 3e3,
	width: "auto",
	didOpen: (toast) => {
		const progress = document.createElement('div');
		Object.assign(progress.style, {
			position: 'absolute',
			bottom: '0',
			left: '0',
			width: '100%',
			height: '3px'
		});
		toast.appendChild(progress);

		let width = 100;
		const interval = setInterval(() => {
			width -= 0.5;
			progress.style.width = width + '%';
			if (width <= 0) {
				clearInterval(interval);
			}
		}, 15);

		// Set progress bar color based on the type of alert
		const alertType = Array.from(toast.classList)
			.find(cls => cls.startsWith('swal2-icon'))
			.replace('swal2-icon-', '');

		switch (alertType) {
			case 'success':
				progress.classList.add('bg-success');
				toast.classList.add('text-success');
				break;
			case 'info':
				progress.classList.add('bg-info');
				toast.classList.add('text-info');
				break;
			case 'warning':
				progress.classList.add('bg-warning');
				toast.classList.add('text-warning');
				break;
			case 'error':
				progress.classList.add('bg-danger');
				toast.classList.add('text-danger');
				break;
			default:
				progress.classList.add('bg-info');
				toast.classList.add('text-info');
		}
	}
});

up.compiler('.toast', function (element, data) {
	Toast.fire({
		icon: data.type,
		title: data.text
	})
});

up.compiler('.tom-select', function (element) {
	let plugins = ['dropdown_input'];
	if (element.hasAttribute('multiple')) {
		plugins.push('remove_button');
	}
	new TomSelect(element, {plugins});
})

up.on('app:map-loaded', function (event) {
	$('#map-loading').slideUp();
});

up.on('up:fragment:inserted', (event, fragment) => {
	fragment.classList.add('new-fragment', 'inserted')
	up.util.timer(0, () => fragment.classList.remove('inserted'))
	up.util.timer(1000, () => fragment.classList.remove('new-fragment'))
});

window.reload = () => {
	// TODO commented the SPA like reload due to css issues, need to fix
	// up.reload({ target: 'body', url: window.location.href })
	window.location.reload();
}


const initMap = function (config) {
	if (window.mapscript) {
		try {
			window.navitimeConfig = config;
			window.map = new mapscript.Map(navitimeConfig.customerId, {
				target: mapSelector,
				center: new mapscript.value.LatLng(34.72383109685586, 135.45281896187524),
				zoomLevel: 9
			});
			window.map.setMapVectorCondition(new mapscript.value.MapVectorCondition({}));
			up.emit('app:map-loaded', {})
		} catch (error) {
			console.error("Error initializing the map:", error);
		}
	} else {
		console.info("mapscript is not loaded or available, retrying after sometime");
		setTimeout(function () {
			initMap(config)
		}, 500)
	}
}

up.on('auth:change', function (event) {
	window.location.href = '/';
});


window.pins = []
window.markers = []
window.routePath = []

window.tripDataAlpine = function () {
	return {
		visits: [],
		totalTime: '',
		selectedVehicle: {id: 0, name: ''},
		showCompleteResult: false,
		data: [],
		date: '',
		solutionId: 0,
		byVehicleItemData: new vis.DataSet(),
		byVehicleGroupData: new vis.DataSet(),
		byVehicleTimeline: null,
		buildTimeLine() {
			this.byVehicleGroupData.clear();
			this.byVehicleItemData.clear();
			const items = this.byVehicleItemData;
			const runDate = this.date;
			this.data.sort((a, b) => {
				const groupIdA = `${a.vehicle.id} : ${a.driver.id}`;
				const groupIdB = `${b.vehicle.id} : ${b.driver.id}`;
				return groupIdA.localeCompare(groupIdB);
			}).forEach(obj => {
				const groupId = `${obj.vehicle.id} : ${obj.driver.id}`
				const groupName = `${obj.vehicle.romsId} : ${obj.driver.name}`;


				//if driver has consolidated orders, add them to the timeline

				let pickups = obj.visits.filter(visit => {
					let consolidatedOrderIds = visit.consolidatedOrderIds.split(',');
					return  visit.taskType === "PICKUP" && consolidatedOrderIds.length > 1;
				});
				let cssClass = '', consolidated = '';
				if(pickups.length > 0) {
					cssClass = 'consolidated';
					consolidated = `<span>集荷統合: ${pickups.length}</span><br>`;
				}

				this.byVehicleGroupData.add({id: groupId, content: `<h5 title="${obj.driver.id}">${obj.driver.name} - ${obj.vehicle.romsId}</h5> ${consolidated}`, className: cssClass});

				/*obj.jobs.forEach(job => {
					this.byVehicleItemData.add({
						group: obj.vehicle.id,
						content: `
								<a href="vehicles/details/${obj.vehicle.id}" up-layer="new popup" up-history="false">
									<span>積み地：${job.pickup.name}</span>
									<br>
									<span>届け先：${job.delivery.name}</span>
									<br>
									<span>到着時刻：${toTime(job.pickup.arrivalTime)}</span>
									<br>
									<span>出発時刻：${toTime(job.delivery.departureTime)}</span>
								</a>
								`,
						start: '2024-11-13T' + toTime(job.pickup.arrivalTime),
						end: '2024-11-13T' + toTime(job.delivery.departureTime),

					});
				});*/

				for (let i = 0; i < obj.visits.length; i++) {
					let job = obj.visits[i];

					let reason = '';

					/*
						開始前、
						終了後、
						運転中、
						車両基地への運転中、
						車両基地での開始前、
					 */
					if(job.taskType === "LUNCH") {
						let lunchType = '';
						if(job.reason === 'DURING_DRIVING') {
							lunchType = '運転中';
						} else if(job.reason === 'BEFORE_START') {
							lunchType = '開始前';
						} else if(job.reason === 'DURING_DRIVING_TO_DEPOT') {
							lunchType = '車両基地への運転中';
						} else if(job.reason === 'AFTER_END') {
							lunchType = '終了後';
						} else if(job.reason === 'BEFORE_START_AT_DEPOT') {
							lunchType = '車両基地での開始前';
						}
						reason = `「${lunchType}」`;
					}

					let visitType = job.taskTypeJapanese;
					let orderId = job.romsJobId;
					let itemName = job.visit.category;

					if(['DEPOT_START', 'DEPOT_END', 'DRIVING', 'CLEANING', 'WAITING', 'LUNCH'].includes(job.taskType)) {
						orderId = '';
						itemName = '';
					}

					let consolidatedOrderIds = job.consolidatedOrderIds.split(',');
					if(consolidatedOrderIds.length > 1) {
						visitType += ` (集荷統合: ${consolidatedOrderIds.length})`;
					}



					let content = `
					  <div title="受注: ${job.orderId}">
						<a href="vehicles/details/${obj.vehicle.id}" up-layer="new popup" up-history="false">
						</a>
						<span>${visitType} ${reason} ${orderId ? `<span>${orderId}</span>` : ''}</span>
						<br>
						${itemName ? `品名：${itemName}<br>` : ''}
						${consolidatedOrderIds.length > 1 ? `<span>統合受注： ${job.consolidatedOrderIds}</span><br>` : ''}
						${job.visit.name ? `<span>${job.visit.name}</span><br>` : ''}
						${job.visit.address ? `<span>${job.visit.address}</span><br>` : ''}
						<span>期間: ${toTime(job.duration)}</span><br>
						<span>${toTime(job.startTime)} ~ ${toTime(job.endTime)}</span>
					  </div>
					`;
					const start = new Date(runDate + 'T' + toTime(job.startTime)),
						end = new Date(runDate + 'T' + toTime(job.endTime));

					let taskTypeClass = `task-${job.taskType.toLowerCase()}`;

					if(consolidatedOrderIds.length > 1) {
						taskTypeClass += ' consolidated';
					}

					if(job.taskType === "LUNCH") {
						if(job.reason.indexOf('DURING')>-1)
						taskTypeClass = `task-lunch-overlap`;
					}

					const data = { group: groupId, groupName, content, start, end, index: job.taskIndex,
						jobId: job.orderId, romsJobId: job.romsJobId, vehicleId: obj.vehicle.id, driverId: obj.driver.id,
						className: taskTypeClass
					};

					if(obj.driver.id === 193) {
						console.log(this.byVehicleItemData.length);
					}
					this.byVehicleItemData.add(data);
					if(obj.driver.id === 193) {
						console.log(this.byVehicleItemData.length);
						console.log(data);
					}
				}
			});

			const byVehiclePanel = document.getElementById("byVehiclePanel");
			let startDate = new Date(`${runDate}T00:25:00`);
			const endDate = new Date(startDate);
			endDate.setDate(endDate.getDate() + 1);
			const solutionId = this.solutionId;
			const byVehicleTimelineOptions = {
				locale: 'ja',
				timeAxis: {scale: "minute", step: 5},
				orientation: {axis: "top"},
				xss: {disabled: true}, // Items are XSS safe through JQuery
				stack: false,
				stackSubgroups: false,
				editable: {
					add: false,         // add new items by double tapping
					updateTime: false,  // drag items horizontally
					updateGroup: false, // drag items from one group to another
					remove: false,       // delete an item by tapping the delete button top right
					overrideItems: false  // allow these options to override item.editable
				},
				selectable: true,
				onMove: function (item, callback) {
					const oldItems = items.get();
					const oldItem = oldItems.find(oi => oi.jobId === item.jobId);

					const groupChanged = oldItem.group !== item.group;
					const newDriverVehicle = item.group.split(" : ");
					console.log(`Group changed: ${groupChanged}, old: ${oldItem.group}, new: ${item.group}`);
					if (newDriverVehicle.length !== 2) {
						console.error("Invalid group format:", item.group);
						callback(null);
						return;
					}
					const group = item.group;
					const groupItems = oldItems.filter(i => i.group === group);

					// New order with the moved item
					const newGroupItems = groupItems
						.filter(i => i.jobId !== item.jobId)
						.concat(item)
						.sort((a, b) => new Date(a.start) - new Date(b.start));

					// Save original indexes mapped by jobId
					const originalIndexMap = {};
					groupItems.forEach(i => {
						originalIndexMap[i.jobId] = i.index;
					});
					const changes = [];

					newGroupItems.forEach((itm, newIndex) => {
						const oldIndex = originalIndexMap[itm.jobId];
						changes.push({
							oldIndex,
							newIndex,
							changed: oldIndex !== newIndex,
							jobId: itm.jobId,
							solutionId,
							vehicleId: itm.vehicleId,
							driverId: itm.driverId,
						});
					});

					if (!changes.some(i => i.changed)) {
						console.log("No index changes. Cancelling move.");
						callback(null);
						return;
					}

					const data = {
						solutionId,
						jobId: item.jobId,
						fromVehicleId: item.vehicleId,
						fromDriverId: item.driverId,
						toDriverId: +newDriverVehicle[1],
						toVehicleId: +newDriverVehicle[0],
						groupChanged,
						changes
					};
					console.log("data with changes: ", data);
					up.render({
						url:'/solution/update-solution',
						method: 'POST',
						target:'#solution-details',
						scroll: 'target',
						payload: JSON.stringify(data),
						contentType: 'application/json',
					}).catch(err => {
						console.log(err);
					})
					callback(item);
				},
				zoomMin: 1000 * 60 * 60 * 2, // 1.5 hours in milliseconds
				zoomMax: 1000 * 60 * 60 * 7, // to display around working hours
				start: startDate, // Set the desired start date
				end: endDate // Set the desired end date
			};
			if (this.byVehicleTimeline) {
				this.byVehicleTimeline.destroy();
			}

			// ➕ Highlight Lunch Time (12:00–14:00) as background items
			const lunchBackgrounds = [];
			const lunchStart = new Date(`${runDate}T12:00:00`);
			const lunchEnd = new Date(`${runDate}T14:00:00`);

			// Loop for each group (vehicle/driver) to apply lunch background per row
			this.byVehicleGroupData.forEach(group => {
				lunchBackgrounds.push({
					id: `lunch-${group.id}`,
					group: group.id,
					start: lunchStart,
					end: lunchEnd,
					type: 'background',
					content: '昼休み',
					className: 'lunch-time'
				});
			});

			this.byVehicleItemData.add(lunchBackgrounds);


			this.byVehicleTimeline = new vis.Timeline(byVehiclePanel, this.byVehicleItemData, this.byVehicleGroupData, byVehicleTimelineOptions);
		},
		init() {
			this.data = up.data(document.querySelector("#data"));
			const solutionInfo = document.querySelector("#solution-info").dataset;
			this.solutionId = +solutionInfo.solutionId;
			this.date = solutionInfo.date;
			this.buildTimeLine();
		},
		onVehicleChange(vehicle) {
			this.selectedVehicle = vehicle;
			this.visits = [];
			this.totalTime = '';

			deleteMarkers();
			let selectedVehicleData = this.data.find(obj => obj.vehicle.id === this.selectedVehicle.id);
			console.log(selectedVehicleData);
			if (!selectedVehicleData) {
				return;
			}
			addMarkersAndPaths(selectedVehicleData, this);
		}
	}
}


function addMarkersAndPaths(obj, alpineComponent) {
	for (const v of obj.visits) {
		const info = new mapscript.value.GLMarkerIconInfo({
			icon: '/static/pin.png',
		});
		let visit = v.visit;
		const position = new mapscript.value.LatLng(visit.lat, visit.lon);
		let marker = new mapscript.object.GLMarker({position, info});

		window.map.addGLMarker(marker);
		const infoWindow = new mapscript.object.InfoWindow({
			content: `
                  <h5>${visit.name}</h5> 到着時刻: ${toTime(visit.arrivalTime)} <br>
                  サービス期間: ${toTime(visit.serviceDuration)} <br>
                  出発時間: ${toTime(visit.departureTime)}`, position
		});
		infoWindow.close();
		window.map.addInfoWindow(infoWindow);
		marker.addEventListener("click", () => {
			if (infoWindow.isOpen()) {
				infoWindow.close();
				return;
			}
			infoWindow.open();
		});
		window.markers.push(marker);
	}

	let wayPoints = [];
	for (let i = 1; i < obj.track.length - 1; i++) {
		if(obj.track[i].lat === 0) {
			continue;
		}
		wayPoints.push({lat: obj.track[i].lat, lon: obj.track[i].lon, name: obj.visits[i].name});
	}
	let DateTime = alpineComponent.date + 'T09:00:00';

	// ルート線を消去します
	if (routePath.length !== 0) {
		for (let i = 0; i < routePath.length; i++) {
			routePath.forEach(function (r) {
				map.removeGeoJsonFigure(r);
			});
		}
		routePath = [];
	}
	// ピンを消去します
	if (window.pins.length !== 0) {
		for (let i = 0; i < window.pins.length; i++) {
			map.removeGLMarker(window.pins[i]);
		}
		window.pins = [];
	}

	let start = {'coord': {'lon': obj.track[0].lon, 'lat': obj.track[0].lat}, 'node': '', name: obj.visits[0].name},
		goal = {'coord': {'lon': obj.track[0].lon, 'lat': obj.track[0].lat}, 'node': '', name: obj.visits[0].name};
		/*params = {
			'start': JSON.stringify(createVertex(start)),
			'goal': JSON.stringify(createVertex(goal)), // 'goal': `${wayPoints[0].lat},${wayPoints[0].lon}`,
			'start_time': DateTime,
			'via': JSON.stringify(wayPoints),
			'signature': navitimeConfig.signature,
			'request_code': navitimeConfig.requestCode,
			'host': navitimeConfig.toBeLoadedFrom,
			'shape': 'true', //'regulation': JSON.stringify({"regulation-type": "large_truck","toll-type": "large","car-body": {"length": 1200,"height": 320,"width": 250,"weight": 24860,"max-loading-capacity": 15300}})
		};
	let base_url = `${navitimeConfig.apiUrl}/${navitimeConfig.customerId}/v1`*/
	let {params, routeUrl} = getNavitimeAPIData(DateTime, start, goal, wayPoints);
	searchRoute(params, routeUrl, alpineComponent);
}

function getNavitimeAPIData(startTime, start, goal, wayPoints) {
	let params = {
		'start': JSON.stringify(createVertex(start)),
		'goal': JSON.stringify(createVertex(goal)),
		'start_time': startTime,
		'signature': navitimeConfig.signature,
		'request_code': navitimeConfig.requestCode,
		'host': navitimeConfig.toBeLoadedFrom,
		'shape': 'true',
	};
	if(wayPoints.length > 0) {
		params.via = JSON.stringify(wayPoints);
	}
	return {params, routeUrl: `${navitimeConfig.apiUrl}/${navitimeConfig.customerId}/v1/route_car`};

}


function deleteMarkers() {
	for (const marker of window.markers) {
		window.map.removeGLMarker(marker);
	}
	window.markers = [];


	for (const path of window.routePath) {
		window.map.removeGeoJsonFigure(path);
	}
	window.routePath = [];
}


function searchRoute(params, route_url, alpineComponent) {
	$.ajax({url: route_url, data: params, type: 'GET', dataType: 'jsonp', jsonp: 'callback', jsonpCallback: 'route',})
		.done(function (json) {
			if (json.items.length === 0) {
				console.log('no data');
				return;
			}

			window.sections = json.items[0].sections;
			let { totalTime, visits} = tripData(window.sections);
			alpineComponent.totalTime = totalTime;
			alpineComponent.visits = visits;
			let pinIndex = 0;
			window.sections.forEach(function (section, i) {
				if (section.type !== 'point' || i !== 0 && i !== sections.length - 1 && !section.with_via) {
					return true;
				}
				section.img = '/static/pin.png';
			});

			drawRouteSections();
			const figure = new mapscript.value.GeoJsonFigureCondition(json.items[0].shapes, {
				isRouteShape: true, showRouteArrow: true, coordUnit: "degree", polyline: {
					color: new mapscript.value.Color(0, 1, 0, 0)
				}
			});
			window.routePath.push(figure);
			const [lng1, lat1, lng2, lat2] = json.items[0].shapes.bbox;
			const rect = mapscript.util.locationsToLatLngRect([new mapscript.value.LatLng(lat1, lng1), new mapscript.value.LatLng(lat2, lng2)]);
			window.map.moveBasedOnLatLngRect(rect, true);
			window.map.addGeoJsonFigure(figure);
		})
		.fail(function (XMLHttpRequest, textStatus, errorThrown) {
			console.log("XMLHttpRequest : " + XMLHttpRequest.status);
			console.log("textStatus     : " + textStatus);
			console.log("errorThrown    : " + errorThrown.message);
		})
}

function tripData(data) {
	const points = [];
	const moves = [];
	const visits = [];
	let currentTime = 32400000 // Set to 9 AM
	let totalTravelTime = 0;

	data.forEach(item => {
		if (item.type === 'point') {
			points.push(item);
		} else if (item.type === 'move') {
			moves.push(item);
			totalTravelTime += item.time;
		}
	});


	if (points.length > 0) {
		const startingPoint = points[0];
		visits.push({
			id: 1,
			name: startingPoint.name,
			arrivalTime: currentTime,
			departureTime: currentTime
		});
	}

	for (let i = 1; i < points.length; i++) {
		if (i - 1 < moves.length) {
			const moveTime = moves[i - 1].time;
			const arrivalTime = currentTime + (moveTime * 60000);
			const departureTime = arrivalTime + (20 * 60000); // Add 20 minutes wait time

			visits.push({
				id: i + 1,
				name: points[i].name,
				arrivalTime: arrivalTime,
				departureTime: departureTime
			});

			currentTime = departureTime;
		}
	}

	visits[visits.length - 1].departureTime = null;

	return {
		totalTime : toTime(totalTravelTime * 60000),
		visits : visits.map(value => {
			value.arrivalTime = toTime(value.arrivalTime)
			value.departureTime = toTime(value.departureTime)
			return value;
		})
	};
}


function toTime(totalTime) {
	if (totalTime == null) {
		return '00:00';
	}
	const hours = Math.floor(totalTime / (1000 * 60 * 60));
	const minutes = Math.floor((totalTime % (1000 * 60 * 60)) / (1000 * 60));
	return String(hours).padStart(2, '0') + ':' + String(minutes).padStart(2, '0');
}

function createVertex(spot) {

	let vertex = {lon: spot.coord.lon, lat: spot.coord.lat, name: spot.name};
	if (spot.node) {
		vertex.node = spot.node;
	}
	if (spot.stay_time) {
		vertex['stay-time'] = spot.stay_time;
	}
	return vertex;
}


function drawRouteSections() {
	let parsed = [], tmp = {}, maxIndex = sections.length - 1;

	window.sections.forEach(function (section, i) {
		// point セクションに対する処理
		if (section.type === 'point') {
			// 経由地の場合は強調して色をつけます
			if (section.with_via) {
				tmp.name_style = 'font-weight:bold;';
				tmp.stopper_color = '#3cb371';
				tmp.line_style = 'display: show;';
			}
			// 画像がある場合はそれを設定します
			if (section.img) {
				tmp.img = section.img;
				tmp.show_weather = true;
				tmp.id = String(i);
				if (i != 0) {
					tmp.id = String(i / 2);
				}
			}
			tmp.point_name = section.name;  // 地点名
			// move セクションに対する処理
		} else {
			tmp.link_name = section.line_name;
			tmp.duration = '（' + section.time + '分）';                // 所要時間

			// セクションの出発時刻
			tmp.from_time = section.from_time;
			// セクションの到着時刻
			tmp.to_time = section.to_time;
		}

		// 起点の場合
		if (i === 0) {
			tmp.name_style = 'font-weight:bold';
			tmp.stopper_color = '#4169e1'
			// 終点の場合
		} else if (i === maxIndex) {
			tmp.name_style = 'font-weight:bold';
			tmp.stopper_color = '#cd5c5c';
			tmp.line_style = 'display: none;';
			parsed.push(tmp);
			// それ以外かつ move セクションだった場合
		} else if (section.type === 'move') {
			parsed.push(tmp);
			tmp = {};
		}
	});

}
